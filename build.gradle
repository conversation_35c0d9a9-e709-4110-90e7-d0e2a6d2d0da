import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
	id 'org.springframework.boot' version '3.2.5' apply false
	id 'io.spring.dependency-management' version '1.1.4' apply false
	id 'com.google.cloud.tools.jib' version '3.4.2' apply false
	id "com.github.johnrengelman.shadow" version "8.1.1" apply false
	id "com.google.protobuf" version "0.9.4" apply false
}

subprojects {
	apply plugin: 'java'
	apply plugin: "io.spring.dependency-management"

	sourceCompatibility = JavaVersion.VERSION_17

	dependencyManagement {
		imports {
			mavenBom SpringBootPlugin.BOM_COORDINATES
		}
		dependencies {
			dependency 'io.jsonwebtoken:jjwt-api:0.11.5'
			dependency 'io.jsonwebtoken:jjwt-impl:0.11.5'
			dependency 'io.jsonwebtoken:jjwt-jackson:0.11.5'
		}
	}

	repositories {
		// Define repositories for dependencies
		mavenCentral()
	}

	dependencies {
		// Define common dependencies for all subprojects
		// ECS logging
		implementation platform('co.elastic.logging:logback-ecs-encoder:1.6.0')

		// azure
		implementation platform('com.azure:azure-sdk-bom:1.2.23')

		// aws bom
		implementation platform('software.amazon.awssdk:bom:2.20.123')

		// reactor-bom
		implementation platform('io.projectreactor:reactor-bom:2023.0.6')

		// micrometer
		implementation platform('io.micrometer:micrometer-bom:1.12.4')
		implementation platform('io.opentelemetry.instrumentation:opentelemetry-instrumentation-bom-alpha:2.7.0-alpha')

		// otel
		implementation(platform("io.opentelemetry:opentelemetry-bom:1.46.0"))

		// Reactive Relational Database Connectivity
		implementation platform('io.r2dbc:r2dbc-bom:Arabba-SR8')

		// vault
		implementation(platform("org.springframework.cloud:spring-cloud-vault-dependencies:4.1.3"))

		// lombok
		compileOnly 'org.projectlombok:lombok'
		annotationProcessor 'org.projectlombok:lombok'
		testCompileOnly 'org.projectlombok:lombok'
		testAnnotationProcessor 'org.projectlombok:lombok'

		// gRPC
		implementation platform("io.grpc:grpc-bom:1.68.1")
		implementation platform("com.google.protobuf:protobuf-java:3.25.5")
		implementation platform("javax.annotation:javax.annotation-api:1.3.2")

		// test
		testImplementation platform('org.junit:junit-bom:5.10.0')
		testImplementation 'org.junit.jupiter:junit-jupiter'
		testImplementation 'org.testcontainers:testcontainers-bom:1.20.4'

		// resilience4j
		implementation platform('io.github.resilience4j:resilience4j-bom:2.1.0')

		// reactor-grpc
		implementation 'com.salesforce.servicelibs:reactor-grpc-stub:1.2.4'
		implementation 'com.salesforce.servicelibs:reactor-grpc:1.2.4'
	}

	test {
		useJUnitPlatform()
	}
}

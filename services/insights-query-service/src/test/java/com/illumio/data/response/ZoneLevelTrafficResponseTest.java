package com.illumio.data.response;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.RequestMetadata.ZONE_LEVEL_TRAFFIC;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ZoneLevelTrafficResponseTest {
    private ZoneLevelTrafficResponse responseBuilder;

    @BeforeEach
    void setUp() {
        responseBuilder = new ZoneLevelTrafficResponse();
    }

    @Test
    void testBuildResponse() {
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject("SourceZone")).thenReturn("AWS");
        when(resultSetTable.getObject("DestinationZone")).thenReturn("Azure");
        when(resultSetTable.getObject("AggFlowCount")).thenReturn(123);
        when(resultSetTable.getObject("AggByteCount")).thenReturn(456);
        when(resultSetTable.getObject("AggFlowCount1")).thenReturn(321);
        when(resultSetTable.getObject("AggByteCount1")).thenReturn(654);
        
        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(10);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        payload.setFilters(Collections.emptyList());

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        ZoneLevelTrafficResponse response = responseBuilder.buildResponse(resultSetTable,  requestContext);

        assertEquals(ZONE_LEVEL_TRAFFIC.getWidgetId(), response.getWidgetId());
        assertEquals("Zone Level Traffic", response.getTitle());

        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());
        assertEquals(Collections.emptyList(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        // Expected row format: [sourceZone, destZone, "20%", flowCount, "15%", sentBytes]
        // In the stub: ["AWS", "Azure", "20%", 123, "15%", 456]
        assertEquals(6, row.size());
        assertEquals("AWS", row.get(0));
        assertEquals("Azure", row.get(1));
        assertEquals(321L, row.get(2));
        assertEquals(123L, row.get(3));
        assertEquals(654L, row.get(4));
        assertEquals(456L, row.get(5));
    }
}

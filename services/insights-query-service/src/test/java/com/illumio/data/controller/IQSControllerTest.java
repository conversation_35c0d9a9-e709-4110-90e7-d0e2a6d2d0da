package com.illumio.data.controller;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.Filters;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.response.RiskyServicesTrafficResponse;
import com.illumio.data.service.DefaultValues;
import com.illumio.data.service.MetricRecordService;
import com.illumio.data.service.RequestPayloadValidation;
import com.illumio.data.service.RequestRouter;
import com.illumio.data.service.RiskyServiceInfo;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.illumio.data.model.constants.RequestMetadata.RISKY_SERVICE_TRAFFIC;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
public class IQSControllerTest {
    private WebTestClient webTestClient;
    @Autowired
    private IQSController iqsController;
    @Mock
    private RequestRouter requestRouter;
    @Mock
    private RiskyServiceInfo riskyServiceInfo;
    @Mock
    private RequestPayloadValidation requestPayloadValidation;
    @Mock
    InsightsServiceConfiguration config;
    @Mock
    DefaultValues defaultValues;
    @Mock
    MetricRecordService metricRecordService;

    private MeterRegistry meterRegistry = new SimpleMeterRegistry();


    private static final String BASE_URL = "/api/v1/tenant/testTenant/insights";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        meterRegistry = new SimpleMeterRegistry();
        this.iqsController = new IQSController(
                requestRouter,
                requestPayloadValidation,
                config,
                defaultValues,
                meterRegistry,
                metricRecordService
        );
        this.webTestClient = WebTestClient.bindToController(iqsController).build();
    }

    @Test
    void testGetWidgetData_Success() {
        RiskyServicesTrafficResponse riskyServicesTrafficResponse = new RiskyServicesTrafficResponse(riskyServiceInfo);
        riskyServicesTrafficResponse.setWidgetId("1234");

        String widgetId = "1234";
        String pageId = "1";
        String tenantId = "testTenant";

        List<Filters> filters = List.of(Filters.builder()
                        .categoryName("port")
                        .categoryType("number")
                        .categoryValue(List.of(443))
                .build());
        List<SortByFields> sortByFields = List.of(SortByFields.builder()
                        .field("bytes")
                        .order("desc")
                .build());

        RequestPayload requestPayload = RequestPayload.builder()
                .filters(filters)
                .sortByFields(sortByFields)
                .build();
        RequestMetadata requestMetadata = RISKY_SERVICE_TRAFFIC;

        doNothing().when(defaultValues).setPayloadDefaults(config,requestPayload);
        when(requestPayloadValidation.validateRequest(requestPayload, RISKY_SERVICE_TRAFFIC))
                .thenReturn("");
        when(requestRouter.routeRequest(any(RequestMetadata.class), any(RequestContext.class)))
                .thenReturn(Mono.just(ResponseEntity.status(HttpStatus.OK).build()));

        webTestClient.post()
                .uri(BASE_URL + "/" + pageId + "/widget/" + widgetId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestPayload)
                .exchange()
                .expectStatus().isOk()
                .expectBody(Object.class);

        Mockito.verify(requestRouter).routeRequest(eq(requestMetadata), any(RequestContext.class));
    }

    @Test
    void testGetWidgetData_InvalidWidgetId() {
        String widgetId = "invalidWidget";
        String pageId = "somePageId";

        RequestPayload requestPayload = RequestPayload.builder().build();

        try (MockedStatic<RequestMetadata> mockedStatic = Mockito.mockStatic(RequestMetadata.class)) {
            mockedStatic.when(() -> RequestMetadata.getRequestMetadataForWidgetId(widgetId))
                    .thenThrow(new IllegalArgumentException("Invalid widget ID: " + widgetId));

            webTestClient.post()
                    .uri(BASE_URL + "/" + pageId + "/widget/" + widgetId)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestPayload)
                    .exchange()
                    .expectStatus()
                    .isBadRequest();
        }

        Mockito.verifyNoInteractions(requestRouter);
    }

    @Test
    void testGetWidgetData_ErrorFromRouter() {
        String widgetId = "1234";
        String pageId = "1";
        String tenantId = "testTenant";

        RequestPayload requestPayload = RequestPayload.builder().build();
        RequestMetadata requestMetadata = RISKY_SERVICE_TRAFFIC;

        doNothing().when(defaultValues).setPayloadDefaults(config,requestPayload);
        when(requestRouter.routeRequest(eq(requestMetadata),any(RequestContext.class)))
                .thenReturn(Mono.error(new RuntimeException()));
        when(requestPayloadValidation.validateRequest(requestPayload, RISKY_SERVICE_TRAFFIC))
                .thenReturn("");


        webTestClient.post()
                .uri(BASE_URL + "/" + pageId + "/widget/" + widgetId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestPayload)
                .exchange()
                .expectStatus()
                .is5xxServerError();

        Mockito.verify(requestRouter).routeRequest(eq(requestMetadata), any(RequestContext.class));
    }

    @Test
    void testGetWidgetData_WithShowAllData() {
        RiskyServicesTrafficResponse riskyServicesTrafficResponse = new RiskyServicesTrafficResponse(riskyServiceInfo);
        riskyServicesTrafficResponse.setWidgetId("1234");

        String widgetId = "1234";
        String pageId = "1";
        String tenantId = "testTenant";
        Boolean showAllData = true; // New Optional Parameter

        List<Filters> filters = List.of(Filters.builder()
                .categoryName("port")
                .categoryType("number")
                .categoryValue(List.of(443))
                .build());
        List<SortByFields> sortByFields = List.of(SortByFields.builder()
                .field("bytes")
                .order("desc")
                .build());

        RequestPayload requestPayload = RequestPayload.builder()
                .filters(filters)
                .sortByFields(sortByFields)
                .build();
        RequestMetadata requestMetadata = RISKY_SERVICE_TRAFFIC;

        doNothing().when(defaultValues).setPayloadDefaults(config, requestPayload);
        when(requestPayloadValidation.validateRequest(requestPayload, RISKY_SERVICE_TRAFFIC))
                .thenReturn("");
        when(requestRouter.routeRequest(any(RequestMetadata.class), any(RequestContext.class)))
                .thenReturn(Mono.just(ResponseEntity.status(HttpStatus.OK).build()));

        webTestClient.post()
                .uri(BASE_URL + "/" + pageId + "/widget/" + widgetId + "?showAllData=" + showAllData) // Pass showAllData
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestPayload)
                .exchange()
                .expectStatus().isOk()
                .expectBody(Object.class);

        Mockito.verify(requestRouter).routeRequest(eq(requestMetadata), any(RequestContext.class));
    }

}

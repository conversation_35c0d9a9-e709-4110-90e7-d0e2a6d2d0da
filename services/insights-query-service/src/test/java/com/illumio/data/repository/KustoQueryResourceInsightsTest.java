package com.illumio.data.repository;
import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.model.constants.TableType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class KustoQueryResourceInsightsTest {
    private KustoQueryRepository repository;
    private RequestPayload payload;
    private final String tenantId = "testTenant";
    private RequestContext requestContext;

    @BeforeEach
    void setUp() {
        repository = new KustoQueryRepository();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-03-05T00:00:00Z").endTime("2025-04-03T23:59:59Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-02-01T00:00:00Z").endTime("2025-02-28T00:00:00Z").build();

        Pagination pagination = Pagination.builder().build();
        pagination.setPageNumber(1);
        pagination.setRowLimit(10);

        payload = RequestPayload.builder().build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        payload.setPagination(pagination);
        payload.setFilters(Collections.emptyList());
        payload.setSortByFields(Collections.emptyList());

        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_RiskyTraffic_Hourly",
                "Insights_RiskyTraffic_Hourly",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE);
        requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .requestPayload(Optional.of(payload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();
    }

    @Test
    void testGetQueryString_RiskyTrafficByRole() {
        String query = repository.getQueryString(requestContext, RequestMetadata.RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS);
        assertNotNull(query);
        assertTrue(query.contains("RiskyTraffic"), "Query should contain risky traffic database name");
        assertTrue(query.contains("let sourceCurrFLOWS"), "Query should contain source current timeframe data");
        assertTrue(query.contains("let sourcePrevFLOWS"), "Query should contain source previous timeframe data");
        assertTrue(query.contains("join kind=leftouter (sourcePrevFLOWS)"), "Query should join curr and prev data for source");
        assertTrue(query.contains("destinationCurrFLOWS"), "Query should contain destination current timeframe data");
        assertTrue(query.contains("destinationPrevFLOWS"), "Query should contain destination previous timeframe data");
        assertTrue(query.contains("join kind=leftouter (destinationPrevFLOWS)"), "Query should join curr and prev data for destination");
        assertTrue(query.contains("union sourceFLOWS, destinationFLOWS"), "Query should contain union curr and prev data for source");
        assertTrue(query.contains("let sourceCurrBYTES"), "Query should contain source current bytes data");
        assertTrue(query.contains("let sourcePrevBYTES"), "Query should contain source previous bytes data");
        assertTrue(query.contains("join kind=leftouter (sourcePrevBYTES)"), "Query should join curr and prev data for source");
        assertTrue(query.contains("destinationCurrBYTES"), "Query should contain destination current bytes data");
        assertTrue(query.contains("destinationPrevBYTES"), "Query should contain destination previous bytes data");
        assertTrue(query.contains("join kind=leftouter (destinationPrevBYTES)"), "Query should join curr and prev data for destination");
        assertTrue(query.contains("union sourceBYTES, destinationBYTES"), "Query should contain union curr and prev data for source");
        assertTrue(query.contains("union flows, bytes"), "Query should union the flows and bytes");
    }

    @Test
    void testGetQueryString_MaliciousIpTraffic() {
        String query = repository.getQueryString(requestContext, RequestMetadata.MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS);
        assertNotNull(query);
        assertTrue(query.contains("MaliciousIpTraffic"), "Query should contain malicious traffic database name");
        assertTrue(query.contains("topMaliciousIpInboundFLOWS"), "Query should contain top malicious ip inbound flows");
        assertTrue(query.contains("topMaliciousIpOutboundFLOWS"), "Query should contain top malicious ip outbound flows");
        assertTrue(query.contains("join kind=fullouter topMaliciousIpOutboundFLOWS"), "Query should join inbound and outbound flows");
        assertTrue(query.contains("topMaliciousIpInboundBYTES"), "Query should contain top malicious ip inbound bytes");
        assertTrue(query.contains("topMaliciousIpOutboundBYTES"), "Query should contain top malicious ip outbound bytes");
        assertTrue(query.contains("kind=fullouter topMaliciousIpOutboundBYTES"), "Query should join inbound and outbound bytes");
        assertTrue(query.contains(" TimeSeries = bin("+ Fields.START_TIME.getTableColumnName()+", 1h)"), "Query should create a timeseries on start time");
        assertTrue(query.contains("union flows, bytes"), "Query should union the flows and bytes");
    }

    @Test
    void testGetQueryString_ExternalDataTransfer() {
        String query = repository.getQueryString(requestContext, RequestMetadata.EXTERNAL_DATA_TRANSFER_RESOURCE_INSIGHTS);
        assertNotNull(query);
        assertTrue(query.contains("externalCurrFLOWS"), "Query should contain current external data transfer flows");
        assertTrue(query.contains("externalPrevFLOWS"), "Query should contain previous external data transfer flows");
        assertTrue(query.contains("join kind=leftouter externalPrevFLOWS"), "Query should join current and previous external data transfer flows");
        assertTrue(query.contains("externalCurrBYTES"), "Query should contain external curr bytes");
        assertTrue(query.contains("externalPrevBYTES"), "Query should contain external previous bytes");
        assertTrue(query.contains("join kind=leftouter externalPrevBYTES"), "Query should join current and previous external data transfer bytes");
        assertTrue(query.contains("union flows, bytes"), "Query should union the flows and bytes");
    }

    @Test
    void testGetQueryString_RiskyServiceTrafficResourceInsights() {
        String query = repository.getQueryString(requestContext, RequestMetadata.RISKY_SERVICES_TRAFFIC_RESOURCE_INSIGHTS);
        assertNotNull(query);
        assertTrue(query.contains("Insights_RiskyTraffic_Hourly"), "Query should contain the base table");
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("| summarize"), "Query should include grouping logic");
        assertTrue(query.contains("let totalRows = toscalar(rows | count);"), "Query should include pagination logic");
    }
}

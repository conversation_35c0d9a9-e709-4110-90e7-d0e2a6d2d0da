package com.illumio.data.response;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;
import java.util.Collections;

import static com.illumio.data.model.constants.Fields.CATEGORY_ID;
import static com.illumio.data.model.constants.Fields.ACCOUNT_NAME;
import static com.illumio.data.model.constants.Fields.CLOUD_PROVIDER;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.RequestMetadata.TOP_CATEGORY_WITH_LLM;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TopCategoryWithLlmResponseTest {
    private TopCategoryWithLlmResponse responseBuilder;

    @BeforeEach
    void setUp() {
        responseBuilder = new TopCategoryWithLlmResponse();
    }

    @Test
    void testBuildResponse() {
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject(CATEGORY_ID.getTableColumnName())).thenReturn("category1");
        when(resultSetTable.getObject(ACCOUNT_NAME.getTableColumnName())).thenReturn("accountA");
        when(resultSetTable.getObject(CLOUD_PROVIDER.getTableColumnName())).thenReturn("aws");
        when(resultSetTable.getObject(DESTINATION_EXTERNAL_LABEL.getTableColumnName())).thenReturn("chatgpt");
        when(resultSetTable.getObject(PREVIOUS_COUNT.getTableColumnName())).thenReturn(100L);
        when(resultSetTable.getObject(COUNT.getTableColumnName())).thenReturn(200L);
        when(resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).thenReturn("FLOWS");

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-03-05T00:00:00Z").endTime("2025-04-03T23:59:59Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-02-01T00:00:00Z").endTime("2025-02-28T00:00:00Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(10);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        payload.setFilters(Collections.emptyList());

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        TopCategoryWithLlmResponse response = responseBuilder.buildResponse(resultSetTable, requestContext);

        assertEquals(TOP_CATEGORY_WITH_LLM.getWidgetId(), response.getWidgetId());
        assertEquals("Top Category With LLM Usage", response.getTitle());

        assertEquals(payload.getCurrentTimeFrame(), response.getCurrentTimeFrame());
        assertEquals(payload.getComparisonTimeFrame(), response.getComparisonTimeFrame());
        assertEquals(payload.getPagination(), response.getPagination());

        List<List<Object>> data = response.getData();
        assertNotNull(data);

        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        assertEquals("category1", row.get(0));
        assertEquals("accounta", row.get(1));
        assertEquals("aws", row.get(2));
        assertEquals("chatgpt", row.get(3));
        assertEquals(200L, row.get(4));
        assertEquals(100L, row.get(5));
        assertEquals("FLOWS", row.get(6));
    }
}

package com.illumio.data.service.batch_widget;

import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.model.widget.WidgetProcessingRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class WidgetRequestCreationServiceTest {

    @InjectMocks
    private WidgetRequestCreationService widgetRequestCreationService;
    
    @Test
    void testCreateProcessingRequestsForRegularWidget() {
        // Given
        String regularWidgetId = "1234"; // Not direction or category dependent
        
        try (MockedStatic<RequestMetadata> mockedStatic = mockStatic(RequestMetadata.class)) {
            // Mock the static methods
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(regularWidgetId)).thenReturn(false);
            mockedStatic.when(() -> RequestMetadata.isCategoryDependentWidgetId(regularWidgetId)).thenReturn(false);
            
            // When
            List<WidgetProcessingRequest> requests = widgetRequestCreationService.createProcessingRequests(List.of(regularWidgetId));
            
            // Then
            assertEquals(1, requests.size());
            WidgetProcessingRequest request = requests.get(0);
            assertEquals(regularWidgetId, request.getWidgetId());
            assertNull(request.getTrafficDirection());
            assertNull(request.getCategoryName());
            assertNull(request.getCategoryValue());
        }
    }
    
    @Test
    void testCreateProcessingRequestsForDirectionalWidget() {
        // Given
        String directionalWidgetId = "2345"; // Direction dependent only
        
        try (MockedStatic<RequestMetadata> mockedStatic = mockStatic(RequestMetadata.class)) {
            // Mock the static methods
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(directionalWidgetId)).thenReturn(true);
            mockedStatic.when(() -> RequestMetadata.isCategoryDependentWidgetId(directionalWidgetId)).thenReturn(false);
            
            // When
            List<WidgetProcessingRequest> requests = widgetRequestCreationService.createProcessingRequests(List.of(directionalWidgetId));
            
            // Then
            assertEquals(2, requests.size());
            
            // Check first request (inbound)
            WidgetProcessingRequest inboundRequest = requests.stream()
                    .filter(r -> "inbound".equals(r.getTrafficDirection()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(directionalWidgetId, inboundRequest.getWidgetId());
            assertEquals("inbound", inboundRequest.getTrafficDirection());
            assertNull(inboundRequest.getCategoryName());
            assertNull(inboundRequest.getCategoryValue());
            
            // Check second request (outbound)
            WidgetProcessingRequest outboundRequest = requests.stream()
                    .filter(r -> "outbound".equals(r.getTrafficDirection()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(directionalWidgetId, outboundRequest.getWidgetId());
            assertEquals("outbound", outboundRequest.getTrafficDirection());
            assertNull(outboundRequest.getCategoryName());
            assertNull(outboundRequest.getCategoryValue());
        }
    }
    
    @Test
    void testCreateProcessingRequestsForCategoryWidget() {
        // Given
        String categoryWidgetId = "3456"; // Category dependent only
        
        try (MockedStatic<RequestMetadata> mockedStatic = mockStatic(RequestMetadata.class)) {
            // Mock the static methods
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(categoryWidgetId)).thenReturn(false);
            mockedStatic.when(() -> RequestMetadata.isCategoryDependentWidgetId(categoryWidgetId)).thenReturn(true);
            
            // When
            List<WidgetProcessingRequest> requests = widgetRequestCreationService.createProcessingRequests(List.of(categoryWidgetId));
            
            // Then
            assertEquals(2, requests.size());
            
            // Check subscriptions request
            WidgetProcessingRequest subscriptionsRequest = requests.stream()
                    .filter(r -> "subscriptions".equals(r.getCategoryValue()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(categoryWidgetId, subscriptionsRequest.getWidgetId());
            assertNull(subscriptionsRequest.getTrafficDirection());
            assertEquals("category", subscriptionsRequest.getCategoryName());
            assertEquals("subscriptions", subscriptionsRequest.getCategoryValue());
            
            // Check tenants request
            WidgetProcessingRequest tenantsRequest = requests.stream()
                    .filter(r -> "tenants".equals(r.getCategoryValue()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(categoryWidgetId, tenantsRequest.getWidgetId());
            assertNull(tenantsRequest.getTrafficDirection());
            assertEquals("category", tenantsRequest.getCategoryName());
            assertEquals("tenants", tenantsRequest.getCategoryValue());
        }
    }
    
    @Test
    void testCreateProcessingRequestsForCombinedWidget() {
        // Given
        String combinedWidgetId = "3344"; // Both direction and category dependent
        
        try (MockedStatic<RequestMetadata> mockedStatic = mockStatic(RequestMetadata.class)) {
            // Mock the static methods
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(combinedWidgetId)).thenReturn(true);
            mockedStatic.when(() -> RequestMetadata.isCategoryDependentWidgetId(combinedWidgetId)).thenReturn(true);
            
            // When
            List<WidgetProcessingRequest> requests = widgetRequestCreationService.createProcessingRequests(List.of(combinedWidgetId));
            
            // Then
            assertEquals(4, requests.size());
            
            // Check inbound subscriptions request
            WidgetProcessingRequest inboundSubscriptionsRequest = requests.stream()
                    .filter(r -> "inbound".equals(r.getTrafficDirection()) && "subscriptions".equals(r.getCategoryValue()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(combinedWidgetId, inboundSubscriptionsRequest.getWidgetId());
            assertEquals("inbound", inboundSubscriptionsRequest.getTrafficDirection());
            assertEquals("category", inboundSubscriptionsRequest.getCategoryName());
            assertEquals("subscriptions", inboundSubscriptionsRequest.getCategoryValue());
            
            // Check outbound subscriptions request
            WidgetProcessingRequest outboundSubscriptionsRequest = requests.stream()
                    .filter(r -> "outbound".equals(r.getTrafficDirection()) && "subscriptions".equals(r.getCategoryValue()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(combinedWidgetId, outboundSubscriptionsRequest.getWidgetId());
            assertEquals("outbound", outboundSubscriptionsRequest.getTrafficDirection());
            assertEquals("category", outboundSubscriptionsRequest.getCategoryName());
            assertEquals("subscriptions", outboundSubscriptionsRequest.getCategoryValue());
            
            // Check inbound tenants request
            WidgetProcessingRequest inboundTenantsRequest = requests.stream()
                    .filter(r -> "inbound".equals(r.getTrafficDirection()) && "tenants".equals(r.getCategoryValue()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(combinedWidgetId, inboundTenantsRequest.getWidgetId());
            assertEquals("inbound", inboundTenantsRequest.getTrafficDirection());
            assertEquals("category", inboundTenantsRequest.getCategoryName());
            assertEquals("tenants", inboundTenantsRequest.getCategoryValue());
            
            // Check outbound tenants request
            WidgetProcessingRequest outboundTenantsRequest = requests.stream()
                    .filter(r -> "outbound".equals(r.getTrafficDirection()) && "tenants".equals(r.getCategoryValue()))
                    .findFirst()
                    .orElseThrow();
            assertEquals(combinedWidgetId, outboundTenantsRequest.getWidgetId());
            assertEquals("outbound", outboundTenantsRequest.getTrafficDirection());
            assertEquals("category", outboundTenantsRequest.getCategoryName());
            assertEquals("tenants", outboundTenantsRequest.getCategoryValue());
        }
    }
    
    @Test
    void testCreateProcessingRequestsHandlesError() {
        // Given
        String errorWidgetId = "error";
        
        try (MockedStatic<RequestMetadata> mockedStatic = mockStatic(RequestMetadata.class)) {
            // Mock the static methods to throw exception
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(errorWidgetId))
                    .thenThrow(new IllegalArgumentException("Test exception"));
            
            // When
            List<WidgetProcessingRequest> requests = widgetRequestCreationService.createProcessingRequests(List.of(errorWidgetId));
            
            // Then - should handle error and create a default request
            assertEquals(1, requests.size());
            WidgetProcessingRequest request = requests.get(0);
            assertEquals(errorWidgetId, request.getWidgetId());
            assertNull(request.getTrafficDirection());
            assertNull(request.getCategoryName());
            assertNull(request.getCategoryValue());
        }
    }
    
    @Test
    void testCreateProcessingRequestsMultipleWidgets() {
        // Given
        String regularWidgetId = "1234";
        String directionalWidgetId = "2345";
        String categoryWidgetId = "3456";
        
        try (MockedStatic<RequestMetadata> mockedStatic = mockStatic(RequestMetadata.class)) {
            // Mock the static methods
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(regularWidgetId)).thenReturn(false);
            mockedStatic.when(() -> RequestMetadata.isCategoryDependentWidgetId(regularWidgetId)).thenReturn(false);
            
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(directionalWidgetId)).thenReturn(true);
            mockedStatic.when(() -> RequestMetadata.isCategoryDependentWidgetId(directionalWidgetId)).thenReturn(false);
            
            mockedStatic.when(() -> RequestMetadata.isDirectionDependentWidgetId(categoryWidgetId)).thenReturn(false);
            mockedStatic.when(() -> RequestMetadata.isCategoryDependentWidgetId(categoryWidgetId)).thenReturn(true);
            
            // When
            List<WidgetProcessingRequest> requests = widgetRequestCreationService.createProcessingRequests(
                    Arrays.asList(regularWidgetId, directionalWidgetId, categoryWidgetId));
            
            // Then
            assertEquals(5, requests.size()); // 1 + 2 + 2
            
            // Verify regular widget
            assertEquals(1, requests.stream()
                    .filter(r -> regularWidgetId.equals(r.getWidgetId()) && r.getTrafficDirection() == null)
                    .count());
            
            // Verify directional widget
            assertEquals(2, requests.stream()
                    .filter(r -> directionalWidgetId.equals(r.getWidgetId()))
                    .count());
            
            // Verify category widget
            assertEquals(2, requests.stream()
                    .filter(r -> categoryWidgetId.equals(r.getWidgetId()))
                    .count());
        }
    }
} 
logging:
  level:
    ROOT: INFO

server:
  port:
    8081

spring:
  application:
    name: insights-query-service
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: servlet

insights-config:
  kustoConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    isManagedIdentity: false
    azureClientId: _DO_NOT_COMMIT_
    azureClientSecret: _DO_NOT_COMMIT_
    azureTenantId: _DO_NOT_COMMIT_
  riskyServiceConfig:
    riskyServiceFilePath: "classpath:risky_services.csv"

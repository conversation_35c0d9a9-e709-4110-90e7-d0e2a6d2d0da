package com.illumio.data.response;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;

import static com.illumio.data.model.constants.Fields.IP;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.TIME_SERIES;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.RequestMetadata.MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS;

@Data
@Component
public class MaliciousIpTrafficResourceInsightsResponse implements ResponseBuilder<MaliciousIpTrafficResourceInsightsResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public MaliciousIpTrafficResourceInsightsResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        MaliciousIpTrafficResourceInsightsResponse response = new MaliciousIpTrafficResourceInsightsResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS.getWidgetId());
        response.setTitle("Malicious IP Traffic");
        response.setColumns(Arrays.asList(IP.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), TIME_SERIES.getFieldKey(), COUNT.getFieldKey()));
        response.setColumnTypes(Arrays.asList(IP.getFieldType(), AGGREGATE_FIELD.getFieldType(), TIME_SERIES.getFieldType(), COUNT.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(IP.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName(), TIME_SERIES.getFieldDisplayName(), COUNT.getFieldDisplayName()));

        //Set Request Data
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getCurrentTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        Map<String, Map<String, List<List<Object>>>> groupedData = new HashMap<>();
        while(resultSetTable.next()) {
            String ip = (String) resultSetTable.getObject(IP.getTableColumnName());
            String aggregate_field = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            String timeseries = (String) resultSetTable.getObject(TIME_SERIES.getTableColumnName());
            Long counts = getLongValue(resultSetTable, COUNT.getTableColumnName());

            groupedData
                    .computeIfAbsent(ip, k -> new HashMap<>())
                    .computeIfAbsent(aggregate_field, k -> new ArrayList<>())
                    .add(Arrays.asList(timeseries, counts));
        }
        List<List<Object>> formattedData = groupedData.entrySet().stream()
                .flatMap(entry -> entry.getValue().entrySet().stream()
                        .map(innerEntry -> Arrays.asList(entry.getKey(), innerEntry.getKey(), innerEntry.getValue())))
                .toList();
        response.setData(formattedData);
        return response;
    }

    @Override
    public MaliciousIpTrafficResourceInsightsResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }

}
package com.illumio.data.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.service.RiskyServiceInfo;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.SERVICE;
import static com.illumio.data.model.constants.Fields.THREAT_LEVEL;
import static com.illumio.data.model.constants.RequestMetadata.TOP_SERVICES;

@JsonIgnoreProperties({"riskyServiceInfo"})
@Data
@Component
public class TopServicesResponse implements ResponseBuilder<TopServicesResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    private final RiskyServiceInfo riskyServiceInfo;

    public TopServicesResponse(RiskyServiceInfo riskyServiceInfo) {
        this.riskyServiceInfo = riskyServiceInfo;
    }


    @Override
    public TopServicesResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext) {
        TopServicesResponse response = this;
        RequestPayload payload = requestContext.getRequestPayload().get();

        //Set Widget Specific Metadata
        response.setWidgetId(TOP_SERVICES.getWidgetId());
        response.setTitle("Top Services");
        response.setColumns(Arrays.asList(SERVICE.getFieldKey(), PORT.getFieldKey(), PROTOCOL.getFieldKey(), THREAT_LEVEL.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), AGGREGATE_FIELD.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SERVICE.getFieldType(), PORT.getFieldType(), PROTOCOL.getFieldType(), THREAT_LEVEL.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType(), AGGREGATE_FIELD.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SERVICE.getFieldDisplayName(), PORT.getFieldDisplayName(), PROTOCOL.getFieldDisplayName(), THREAT_LEVEL.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            Integer port = (Integer) resultSetTable.getObject(PORT.getTableColumnName());
            String protocol = (String) resultSetTable.getObject(PROTOCOL.getTableColumnName());
            Integer threatLevel = (Integer) resultSetTable.getObject(THREAT_LEVEL.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long previousCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            String aggrField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            String service = Optional.ofNullable(port)
                    .flatMap(p -> Optional.ofNullable(protocol)
                            .flatMap(pr -> Optional.ofNullable(
                                            riskyServiceInfo.getPortProtoToServiceInfoMap().get(new RiskyServiceInfo.PortProtocolPair(p, pr.toUpperCase())))
                                    .map(RiskyServiceInfo.ServiceInfo::getService)))
                    .orElse("UNKNOWN");


            List<Object> dataRow = new ArrayList<>();
            dataRow.add(service);
            dataRow.add(port);
            dataRow.add(protocol);
            dataRow.add(threatLevel);
            dataRow.add(count);
            dataRow.add(previousCount);
            dataRow.add(aggrField.toUpperCase());

            data.add(dataRow);
        }
        response.setData(data);

        return response;
    }

    @Override
    public TopServicesResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Compresses the response using dictionary/index encoding for maximum token efficiency.
     * This approach creates dictionaries for service names, protocols, and aggregate fields
     * to avoid repeating strings, and uses numeric arrays for the actual data.
     *
     * Format:
     * {
     *   "t": "20250401-20250604",                 // compact time representation
     *   "svc": ["SMB", "HTTP", "SSH", ...],       // service dictionary
     *   "proto": ["TCP", "UDP", ...],             // protocol dictionary
     *   "agg": ["flows", "bytes", ...],           // aggregate field dictionary
     *   "d": [                                    // data rows with numeric values only
     *     [0, 445, 0, 3, 5072, 4210, 1],          // svcIdx, port, protoIdx, threatLevel, count, prevCount, aggFieldIdx
     *     ...
     *   ]
     * }
     * 
     * @return A Map containing the dictionary-compressed response
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();

        // Add compact time representation
        if (currentTimeFrame != null) {
            String currStartTs = currentTimeFrame.getStartTime().replaceAll("[^0-9]", "").substring(0, 8); // Format YYYYMMDD
            String currEndTs = currentTimeFrame.getEndTime().replaceAll("[^0-9]", "").substring(0, 8);
            result.put("ct", currStartTs + "-" + currEndTs);
        }
        if(comparisonTimeFrame != null) {
            String prevStartTs = comparisonTimeFrame.getStartTime().replaceAll("[^0-9]", "").substring(0, 8); // Format YYYYMMDD
            String prevEndTs = comparisonTimeFrame.getEndTime().replaceAll("[^0-9]", "").substring(0, 8);
            result.put("pt", prevStartTs + "-" + prevEndTs);
        }
        
        // Build dictionaries for all string fields
        List<String> serviceDict = new ArrayList<>();
        Map<String, Integer> serviceToIndex = new HashMap<>();
        
        List<String> protoDict = new ArrayList<>();
        Map<String, Integer> protoToIndex = new HashMap<>();
        
        List<String> aggFieldDict = new ArrayList<>();
        Map<String, Integer> aggFieldToIndex = new HashMap<>();
        
        List<List<Object>> compressedData = new ArrayList<>();
        
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 7) continue;
                
                String service = (String) row.get(0);
                Integer port = (Integer) row.get(1);
                String protocol = (String) row.get(2);
                Integer threatLevel = (Integer) row.get(3);
                Long count = ((Number)row.get(4)).longValue();
                Long prevCount = ((Number)row.get(5)).longValue();
                String aggField = (String) row.get(6);
                
                // Original condition was filtering out too much data - fixed to only skip truly empty entries
                // Skip services with BOTH zero counts AND zero ports (they can be inferred)
                // But keep entries with either non-zero counts OR non-zero ports
                if ((count == null || count == 0L) && 
                    (prevCount == null || prevCount == 0L) && 
                    (port == null || port == 0)) {
                    continue;
                }
                
                // Add strings to dictionaries if not already present
                if (!serviceToIndex.containsKey(service)) {
                    serviceToIndex.put(service, serviceDict.size());
                    serviceDict.add(service);
                }
                
                if (!protoToIndex.containsKey(protocol)) {
                    protoToIndex.put(protocol, protoDict.size());
                    protoDict.add(protocol);
                }
                
                if (!aggFieldToIndex.containsKey(aggField)) {
                    aggFieldToIndex.put(aggField, aggFieldDict.size());
                    aggFieldDict.add(aggField);
                }
                
                // Create data row with indices instead of string values
                List<Object> compressedRow = new ArrayList<>();
                compressedRow.add(serviceToIndex.get(service));
                compressedRow.add(port);
                compressedRow.add(protoToIndex.get(protocol));
                compressedRow.add(threatLevel);
                compressedRow.add(count);
                compressedRow.add(prevCount);
                compressedRow.add(aggFieldToIndex.get(aggField));
                
                compressedData.add(compressedRow);
            }
        }
        
        // Add dictionaries and data to result
        result.put("svc", serviceDict);
        result.put("proto", protoDict);
        result.put("agg", aggFieldDict);
        result.put("d", compressedData);
        
        // Add pagination info if needed
        if (pagination != null && pagination.getTotalPages() > 1) {
            Map<String, Integer> pg = new HashMap<>();
            pg.put("p", pagination.getPageNumber());
            pg.put("tp", pagination.getTotalPages());
            result.put("pg", pg);
        }
        
        // Add format indicator and title
        result.put("fmt", "dict-v1");
        result.put("w", widgetId);
        result.put("title", title);
        
        return result;
    }
}
package com.illumio.data.service;

import com.illumio.data.model.RequestContext;
import com.illumio.data.builder.ResponseBuilder;
import reactor.core.publisher.Mono;

public interface DataService<T> {
    <R> Mono<R> getRiskyServiceTraffic(RequestContext requestContext,ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getZoneLevelTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopDestinationRoles(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopWorkloads(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getDestRoleTraffic(RequestContext requestContext,ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopMaliciousIps(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getThreatMap(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopCategoryWithMaliciousIp(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopRoles(RequestContext requestContext,ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopServices(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getExternalDestCategoryTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopSourceRoleTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getExternalGeoTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopSourceTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getExternalServiceTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getLlmInUse(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopCategoryWithLlm(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopSourcesWithLlm(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getThirdPartyInbound(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getThirdPartyOutbound(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopSourceTransferInsightsHub(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopMaliciousIpsInsightsHub(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getRiskyServiceTrafficInsightsHub(RequestContext requestContext,ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopDestinationRolesInsightsHub(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopCrossRegionTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getTopRegionToCountryTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getRiskyTrafficByRolesResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getMaliciousIpTrafficResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getExternalDataTransferResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getUnencryptedServices(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getDoraTopIct(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getDoraCriticalIct(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
    <R> Mono<R> getRiskyServicesTrafficResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder);
}

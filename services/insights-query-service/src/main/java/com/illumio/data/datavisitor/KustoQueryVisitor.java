package com.illumio.data.datavisitor;

import com.illumio.data.KustoQueryClient;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.builder.ResponseBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Optional;

@Getter
@Slf4j
@Component
public class KustoQueryVisitor implements DataVisitor<KustoEntity> {
    private Object result;
    private final KustoQueryClient client;

    @Autowired
    public KustoQueryVisitor(KustoQueryClient client) {
        this.client = client;
    }

    @Override
    public <T> Mono<T> visit(KustoEntity entity, RequestMetadata requestMetadata, RequestContext requestContext,
                             ResponseBuilder<T> responseBuilder) {
        return processQuery(requestMetadata, requestContext, responseBuilder)
                .doOnSubscribe(subscription -> log.debug("Querying Kusto for request type: {}", requestMetadata.getRequestType()))
                .doOnSuccess(response -> {
                    log.debug("Successfully queried Kusto");
                    this.result = response;
                })
                .doOnError(error -> log.error("Error querying Kusto", error));
    }

    private <R> Mono<R> processQuery(RequestMetadata metadata, RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        return client.getResults(metadata, requestContext, responseBuilder);
    }
}

package com.illumio.data.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonValue;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.configuration.ResourceMapConfig;
import com.illumio.data.model.RequestContext;
import com.illumio.data.service.RiskyServiceInfo;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@JsonIgnoreProperties({"riskyServiceInfo"})
@Component
@Data
public class ResourceInsightsResponse implements ResponseBuilder<ResourceInsightsResponse> {
    // This field will be serialized as the top-level JSON array
    List<Map<String, Object>> aggregatedResources;
    private final RiskyServiceInfo riskyServiceInfo;
    private final InsightsServiceConfiguration config;
    private final ResourceMapConfig resourceMapConfig;

    public ResourceInsightsResponse(RiskyServiceInfo riskyServiceInfo,
                                    InsightsServiceConfiguration config,
                                    ResourceMapConfig resourceMapConfig) {
        this.riskyServiceInfo = riskyServiceInfo;
        this.config = config;
        this.resourceMapConfig = resourceMapConfig;
    }

    @JsonValue
    public List<Map<String, Object>> toJson() {
        // Remove empty values recursively before final serialization
        return aggregatedResources.stream()
                .map(this::removeEmptyValuesRecursive)
                .collect(Collectors.toList());
    }

    @Override
    public ResourceInsightsResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext) {
        return null;
    }

    @Override
    public ResourceInsightsResponse buildAggregatedResponse(List<KustoResultSetTable> resultList,
                                                            Optional<MultiValueMap<String, String>> params) {
        // Expecting resultList with five tables: risky services, malicious IPs, ML labels, LLM, and External Data Transfer.
        KustoResultSetTable riskyTrafficData = resultList.get(0);
        KustoResultSetTable maliciousIpData = resultList.get(1);
        KustoResultSetTable mlLabelData = resultList.get(2);
        KustoResultSetTable llmData = resultList.get(3);
        KustoResultSetTable externalDataTransferData = resultList.get(4);

        String illumioUrlBase = config.getUrlConfig().getUiUrl() + "/#/insights/resource-insights?id=";

        List<Map<String, Object>> flattenedList = new ArrayList<>();

        // Process Risky Services: each row becomes a separate JSON object.
        while (riskyTrafficData.next()) {
            Map<String, Object> output = new LinkedHashMap<>();
            // Resource-level fields.
            String resourceId = (String) riskyTrafficData.getObject("ResourceId");
            output.put("ResourceId", resourceId);
            output.put("ResourceInternalId", riskyTrafficData.getObject("ResourceInternalId"));
            output.put("IllumioTenantId", riskyTrafficData.getObject("IllumioTenantId"));
            output.put("ResourceTenantId", riskyTrafficData.getObject("ResourceTenantId"));
            output.put("ResourceSubId", riskyTrafficData.getObject("ResourceSubId"));
            output.put("ResourceRegion", riskyTrafficData.getObject("ResourceRegion"));
            output.put("ResourceVnetId", riskyTrafficData.getObject("ResourceVnetId"));
            output.put("IllumioUrl", illumioUrlBase + resourceId);

            // Build the insight part.
            Map<String, Object> insight = new LinkedHashMap<>();
            insight.put("Name", "Risky Services");

            // Map port/proto to service name.
            String service = Optional.ofNullable(
                            riskyServiceInfo.getPortProtoToServiceInfoMap().get(
                                    new RiskyServiceInfo.PortProtocolPair(
                                            (Integer) riskyTrafficData.getObject("Port"),
                                            ((String) riskyTrafficData.getObject("Proto")).toUpperCase()))
                    ).map(RiskyServiceInfo.ServiceInfo::getService)
                    .orElse("UNKNOWN");
            insight.put("Service", service);
            insight.put("Port", riskyTrafficData.getObject("Port"));
            insight.put("Proto", riskyTrafficData.getObject("Proto"));
            insight.put("Status", riskyTrafficData.getObject("TrafficStatus"));
            insight.put("SrcLabel", riskyTrafficData.getObject("SourceLabel"));
            insight.put("DestLabel", riskyTrafficData.getObject("DestinationLabel"));
            insight.put("TotalSentBytes", getLongValue(riskyTrafficData, "TotalSentBytes"));
            insight.put("TotalReceivedBytes", getLongValue(riskyTrafficData, "TotalReceivedBytes"));
            insight.put("FlowCount", getLongValue(riskyTrafficData, "FlowCount"));
            insight.put("TimeGenerated", riskyTrafficData.getObject("IngestionTime"));

            // Generate a unique ID for this insight from its composite key.
            String compositeKey = resourceId + "|" + riskyTrafficData.getObject("ResourceInternalId") + "|" +
                    riskyTrafficData.getObject("IllumioTenantId") + "|" + riskyTrafficData.getObject("ResourceTenantId") + "|" +
                    riskyTrafficData.getObject("ResourceSubId") + "|" + riskyTrafficData.getObject("ResourceRegion") + "|" +
                    riskyTrafficData.getObject("ResourceVnetId") + "|" + riskyTrafficData.getObject("Port") + "|" +
                    riskyTrafficData.getObject("Proto") + "|" + riskyTrafficData.getObject("TrafficStatus") + "|" +
                    riskyTrafficData.getObject("SourceLabel") + "|" + riskyTrafficData.getObject("DestinationLabel") + "|" +
                    getLongValue(riskyTrafficData, "TotalSentBytes") + "|" + getLongValue(riskyTrafficData, "TotalReceivedBytes") + "|" +
                    getLongValue(riskyTrafficData, "FlowCount") + "|" + riskyTrafficData.getObject("IngestionTime");
            insight.put("UniqueId", UUID.nameUUIDFromBytes(compositeKey.getBytes()).toString());

            output.put("Insights", insight);
            flattenedList.add(output);
        }

        // Process Malicious IPs.
        while (maliciousIpData.next()) {
            Map<String, Object> output = new LinkedHashMap<>();
            String resourceId = (String) maliciousIpData.getObject("ResourceId");
            output.put("ResourceId", resourceId);
            output.put("ResourceInternalId", maliciousIpData.getObject("ResourceInternalId"));
            output.put("IllumioTenantId", maliciousIpData.getObject("IllumioTenantId"));
            output.put("ResourceTenantId", maliciousIpData.getObject("ResourceTenantId"));
            output.put("ResourceSubId", maliciousIpData.getObject("ResourceSubId"));
            output.put("ResourceRegion", maliciousIpData.getObject("ResourceRegion"));
            output.put("ResourceVnetId", maliciousIpData.getObject("ResourceVnetId"));
            output.put("IllumioUrl", illumioUrlBase + resourceId);

            Map<String, Object> insight = new LinkedHashMap<>();
            insight.put("Name", "Top Malicious IPs");
            insight.put("TotalSentBytes", getLongValue(maliciousIpData, "TotalSentBytes"));
            insight.put("TotalReceivedBytes", getLongValue(maliciousIpData, "TotalReceivedBytes"));
            insight.put("FlowCount", getLongValue(maliciousIpData, "FlowCount"));
            insight.put("TimeGenerated", maliciousIpData.getObject("IngestionTime"));

            // Determine if outbound or inbound based on DestIP.
            String destIP = (String) maliciousIpData.getObject("DestIP");
            if (destIP == null || destIP.trim().isEmpty()) {
                // Outbound insight.
                insight.put("SrcIP", maliciousIpData.getObject("SrcIP"));
                insight.put("SrcThreatLevel", maliciousIpData.getObject("SrcThreatLevel"));
                insight.put("SrcIsWellKnown", maliciousIpData.getObject("SrcIsWellknown"));
                insight.put("SrcCity", maliciousIpData.getObject("SrcCity"));
                insight.put("SrcCountry", maliciousIpData.getObject("SrcCountry"));
                insight.put("DestLabel", maliciousIpData.getObject("DestinationLabel"));
                String compositeKey = resourceId + "|" + maliciousIpData.getObject("ResourceInternalId") + "|" +
                        maliciousIpData.getObject("IllumioTenantId") + "|" + maliciousIpData.getObject("ResourceTenantId") + "|" +
                        maliciousIpData.getObject("ResourceSubId") + "|" + maliciousIpData.getObject("ResourceRegion") + "|" +
                        maliciousIpData.getObject("ResourceVnetId") + "|" + maliciousIpData.getObject("SrcIP") + "|" +
                        maliciousIpData.getObject("SrcThreatLevel") + "|" + maliciousIpData.getObject("SrcIsWellknown") + "|" +
                        maliciousIpData.getObject("SrcCity") + "|" + maliciousIpData.getObject("SrcCountry") + "|" +
                        maliciousIpData.getObject("DestinationLabel") + "|" + maliciousIpData.getObject("IngestionTime");
                insight.put("UniqueId", UUID.nameUUIDFromBytes(compositeKey.getBytes()).toString());
            } else {
                // Inbound insight.
                insight.put("DestIP", destIP);
                insight.put("DestThreatLevel", maliciousIpData.getObject("DestThreatLevel"));
                insight.put("DestIsWellKnown", maliciousIpData.getObject("DestIsWellknown"));
                insight.put("DestCity", maliciousIpData.getObject("DestCity"));
                insight.put("DestCountry", maliciousIpData.getObject("DestCountry"));
                insight.put("SrcLabel", maliciousIpData.getObject("SourceLabel"));
                String compositeKey = resourceId + "|" + maliciousIpData.getObject("ResourceInternalId") + "|" +
                        maliciousIpData.getObject("IllumioTenantId") + "|" + maliciousIpData.getObject("ResourceTenantId") + "|" +
                        maliciousIpData.getObject("ResourceSubId") + "|" + maliciousIpData.getObject("ResourceRegion") + "|" +
                        maliciousIpData.getObject("ResourceVnetId") + "|" + destIP + "|" +
                        maliciousIpData.getObject("DestThreatLevel") + "|" + maliciousIpData.getObject("DestIsWellknown") + "|" +
                        maliciousIpData.getObject("DestCity") + "|" + maliciousIpData.getObject("DestCountry") + "|" +
                        maliciousIpData.getObject("SourceLabel") + "|" + maliciousIpData.getObject("IngestionTime");
                insight.put("UniqueId", UUID.nameUUIDFromBytes(compositeKey.getBytes()).toString());
            }
            output.put("Insights", insight);
            flattenedList.add(output);
        }

        // Process ML Labels.
        while (mlLabelData.next()) {
            Map<String, Object> output = new LinkedHashMap<>();
            String resourceId = (String) mlLabelData.getObject("ResourceId");
            output.put("ResourceId", resourceId);
            output.put("ResourceInternalId", mlLabelData.getObject("ResourceInternalId"));
            output.put("IllumioTenantId", mlLabelData.getObject("IllumioTenantId"));
            output.put("ResourceTenantId", mlLabelData.getObject("ResourceTenantId"));
            output.put("ResourceSubId", mlLabelData.getObject("ResourceSubId"));
            output.put("ResourceRegion", mlLabelData.getObject("ResourceRegion"));
            output.put("ResourceVnetId", mlLabelData.getObject("ResourceVnetId"));
            output.put("IllumioUrl", illumioUrlBase + resourceId);

            Map<String, Object> insight = new LinkedHashMap<>();
            insight.put("Name", "ML Labels");
            String srcLabel = (String) mlLabelData.getObject("SourceLabel");
            String destLabel = (String) mlLabelData.getObject("DestinationLabel");
            insight.put("TotalSentBytes", getLongValue(mlLabelData, "TotalSentBytes"));
            insight.put("TotalReceivedBytes", getLongValue(mlLabelData, "TotalReceivedBytes"));
            insight.put("FlowCount", getLongValue(mlLabelData, "FlowCount"));
            insight.put("TimeGenerated", mlLabelData.getObject("IngestionTime"));

            if (srcLabel == null || srcLabel.trim().isEmpty()) {
                insight.put("DestLabel", destLabel);
            } else {
                insight.put("SrcLabel", srcLabel);
            }
            String compositeKey = resourceId + "|" + mlLabelData.getObject("ResourceInternalId") + "|" +
                    mlLabelData.getObject("IllumioTenantId") + "|" + mlLabelData.getObject("ResourceTenantId") + "|" +
                    mlLabelData.getObject("ResourceSubId") + "|" + mlLabelData.getObject("ResourceRegion") + "|" +
                    mlLabelData.getObject("ResourceVnetId") + "|" + srcLabel + "|" + destLabel + "|" +
                    getLongValue(mlLabelData, "TotalSentBytes") + "|" + getLongValue(mlLabelData, "TotalReceivedBytes") + "|" +
                    getLongValue(mlLabelData, "FlowCount") + "|" + mlLabelData.getObject("IngestionTime");
            insight.put("UniqueId", UUID.nameUUIDFromBytes(compositeKey.getBytes()).toString());

            output.put("Insights", insight);
            flattenedList.add(output);
        }

        // Process LLM insights
        if (llmData != null) {
            while (llmData.next()) {
                Map<String, Object> output = new LinkedHashMap<>();
                String resourceId = (String) safeGetObject(llmData, "ResourceId");
                if (resourceId == null) {
                    continue; // Skip this row if ResourceId is missing
                }
                
                output.put("ResourceId", resourceId);
                output.put("ResourceInternalId", safeGetObject(llmData, "ResourceInternalId"));
                output.put("IllumioTenantId", safeGetObject(llmData, "IllumioTenantId"));
                output.put("ResourceTenantId", safeGetObject(llmData, "ResourceTenantId"));
                output.put("ResourceSubId", safeGetObject(llmData, "ResourceSubId"));
                output.put("ResourceRegion", safeGetObject(llmData, "ResourceRegion"));
                output.put("ResourceVnetId", safeGetObject(llmData, "ResourceVnetId"));
                output.put("IllumioUrl", illumioUrlBase + resourceId);

                Map<String, Object> insight = new LinkedHashMap<>();
                insight.put("Name", "LLM Access");
                insight.put("LLMName", safeGetObject(llmData, "LLMName"));
                insight.put("LLMDomain", safeGetObject(llmData, "DestDomain"));
                insight.put("DestRegion", safeGetObject(llmData, "DestRegion"));
                insight.put("TotalSentBytes", safeLongValue(llmData, "TotalSentBytes"));
                insight.put("TotalReceivedBytes", safeLongValue(llmData, "TotalReceivedBytes"));
                insight.put("FlowCount", safeLongValue(llmData, "FlowCount"));
                insight.put("TimeGenerated", safeGetObject(llmData, "IngestionTime"));

                // Generate unique ID - use only fields we know exist
                StringBuilder compositeKeyBuilder = new StringBuilder();
                compositeKeyBuilder.append(resourceId).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "ResourceInternalId")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "IllumioTenantId")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "ResourceTenantId")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "ResourceSubId")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "ResourceRegion")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "ResourceVnetId")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "LLMName")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "DestDomain")).append("|");
                compositeKeyBuilder.append(safeLongValue(llmData, "TotalSentBytes")).append("|");
                compositeKeyBuilder.append(safeLongValue(llmData, "TotalReceivedBytes")).append("|");
                compositeKeyBuilder.append(safeLongValue(llmData, "FlowCount")).append("|");
                compositeKeyBuilder.append(safeGetObject(llmData, "IngestionTime"));
                
                insight.put("UniqueId", UUID.nameUUIDFromBytes(compositeKeyBuilder.toString().getBytes()));

                output.put("Insights", insight);
                flattenedList.add(output);
            }
        }

        // Process External Data Transfer insights
        if (externalDataTransferData != null) {
            while (externalDataTransferData.next()) {
                Map<String, Object> output = new LinkedHashMap<>();
                String resourceId = (String) safeGetObject(externalDataTransferData, "ResourceId");
                if (resourceId == null) {
                    continue; // Skip this row if ResourceId is missing
                }
                
                output.put("ResourceId", resourceId);
                output.put("ResourceInternalId", safeGetObject(externalDataTransferData, "ResourceInternalId"));
                output.put("IllumioTenantId", safeGetObject(externalDataTransferData, "IllumioTenantId"));
                output.put("ResourceTenantId", safeGetObject(externalDataTransferData, "ResourceTenantId"));
                output.put("ResourceSubId", safeGetObject(externalDataTransferData, "ResourceSubId"));
                output.put("ResourceRegion", safeGetObject(externalDataTransferData, "ResourceRegion"));
                output.put("ResourceVnetId", safeGetObject(externalDataTransferData, "ResourceVnetId"));
                output.put("IllumioUrl", illumioUrlBase + resourceId);

                Map<String, Object> insight = new LinkedHashMap<>();
                insight.put("Name", "External Data Transfer");
                insight.put("DestCountry", safeGetObject(externalDataTransferData, "DestCountry"));
                insight.put("DestLabel", safeGetObject(externalDataTransferData, "DestinationExternalLabel"));
                insight.put("DestCategory", safeGetObject(externalDataTransferData, "DestinationExternalLabelCategory"));
                insight.put("DestRegion", safeGetObject(externalDataTransferData, "DestRegion"));
                insight.put("TotalSentBytes", safeLongValue(externalDataTransferData, "TotalSentBytes"));
                insight.put("TotalReceivedBytes", safeLongValue(externalDataTransferData, "TotalReceivedBytes"));
                insight.put("FlowCount", safeLongValue(externalDataTransferData, "FlowCount"));
                insight.put("TimeGenerated", safeGetObject(externalDataTransferData, "IngestionTime"));

                // Generate unique ID - use only fields we know exist
                StringBuilder compositeKeyBuilder = new StringBuilder();
                compositeKeyBuilder.append(resourceId).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "ResourceInternalId")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "IllumioTenantId")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "ResourceTenantId")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "ResourceSubId")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "ResourceRegion")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "ResourceVnetId")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "DestCountry")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "DestinationExternalLabel")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "DestinationExternalLabelCategory")).append("|");
                compositeKeyBuilder.append(safeLongValue(externalDataTransferData, "TotalSentBytes")).append("|");
                compositeKeyBuilder.append(safeLongValue(externalDataTransferData, "TotalReceivedBytes")).append("|");
                compositeKeyBuilder.append(safeLongValue(externalDataTransferData, "FlowCount")).append("|");
                compositeKeyBuilder.append(safeGetObject(externalDataTransferData, "IngestionTime"));
                
                insight.put("UniqueId", UUID.nameUUIDFromBytes(compositeKeyBuilder.toString().getBytes()));

                output.put("Insights", insight);
                flattenedList.add(output);
            }
        }

        if (resourceMapConfig.isEnabled()) {
            addVeInsights(flattenedList);
        }

        this.setAggregatedResources(flattenedList);
        return this;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }

    private Object safeGetObject(KustoResultSetTable resultSetTable, String columnName) {
        try {
            return resultSetTable.getObject(columnName);
        } catch (Exception e) {
            return null;
        }
    }

    private Long safeLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = safeGetObject(resultSetTable, columnName);
        return (value != null && value instanceof Number) ? ((Number) value).longValue() : null;
    }

    private Map<String, Object> removeEmptyValuesRecursive(Map<String, Object> map) {
        Map<String, Object> filtered = new LinkedHashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object cleaned = cleanValue(entry.getValue());
            if (cleaned != null) {
                filtered.put(entry.getKey(), cleaned);
            }
        }
        return filtered;
    }

    @SuppressWarnings("unchecked")
    private Object cleanValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            String s = ((String) value).trim();
            return s.isEmpty() ? null : s;
        }
        if (value instanceof Map) {
            return removeEmptyValuesRecursive((Map<String, Object>) value);
        }
        if (value instanceof List) {
            List<?> list = (List<?>) value;
            List<Object> cleanedList = list.stream()
                    .map(this::cleanValue)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return cleanedList.isEmpty() ? null : cleanedList;
        }
        return value;
    }

    private void addVeInsights(List<Map<String, Object>> aggregatedList) {
        // Create a temporary list to hold new VE insight rows.
        List<Map<String, Object>> veInsightRows = new ArrayList<>();

        // Iterate over the configuration's resource map
        resourceMapConfig.getResourceMap().forEach(rsc -> {
            String resourceId = rsc.getResourceId();
            // Check if there is at least one row already for this resource.
            Optional<Map<String, Object>> baseRowOpt = aggregatedList.stream()
                    .filter(resource -> resourceId.equals(resource.get("ResourceId")))
                    .findFirst();

            if (baseRowOpt.isPresent()) {
                Map<String, Object> baseRow = baseRowOpt.get();

                // Create a new row for the VE insight that copies the resource fields.
                Map<String, Object> newRow = new LinkedHashMap<>();
                newRow.put("ResourceId", baseRow.get("ResourceId"));
                newRow.put("ResourceInternalId", baseRow.get("ResourceInternalId"));
                newRow.put("IllumioTenantId", baseRow.get("IllumioTenantId"));
                newRow.put("ResourceSubId", baseRow.get("ResourceSubId"));
                newRow.put("ResourceRegion", baseRow.get("ResourceRegion"));
                newRow.put("IllumioUrl", baseRow.get("IllumioUrl"));

                // Build the VE insight (now as a single map, not a list entry)
                Map<String, Object> veInsight = new LinkedHashMap<>();
                veInsight.put("Name", "V-E Score");

                // Pull the values from the configuration resource.
                String severityLevel = rsc.getSeverityLevel();
                Double VEScore = rsc.getVEScore();
                Long sentBytes = rsc.getSentBytes();
                Long receivedBytes = rsc.getReceivedBytes();
                Long flowCount = rsc.getFlowCount();
                String ingestionTime = java.time.Instant.now().toString();

                // Create a composite key for generating a unique ID.
                String compositeKey = severityLevel + "|" + VEScore + "|" +
                        sentBytes + "|" + receivedBytes + "|" +
                        flowCount + "|" + ingestionTime;
                veInsight.put("UniqueId", UUID.nameUUIDFromBytes(compositeKey.getBytes()).toString());
                veInsight.put("TimeGenerated", ingestionTime);

                // Add inner insight details directly into the same map.
                veInsight.put("CvssSeverity", severityLevel);
                veInsight.put("VEScore", VEScore);
                veInsight.put("TotalSentBytes", sentBytes);
                veInsight.put("TotalReceivedBytes", receivedBytes);
                veInsight.put("FlowCount", flowCount);

                // Insert VE insight into the row.
                newRow.put("Insights", veInsight);

                // Add this new row to the temporary VE insights list.
                veInsightRows.add(newRow);
            }
        });

        // Append all new VE insight rows into the flattened response.
        aggregatedList.addAll(veInsightRows);
    }
}
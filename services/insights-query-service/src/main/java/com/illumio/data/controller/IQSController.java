package com.illumio.data.controller;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.DefaultValues;
import com.illumio.data.service.MetricRecordService;
import com.illumio.data.service.PostgresMetadataService;
import com.illumio.data.service.RequestPayloadValidation;
import com.illumio.data.service.RequestRouter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.format.DateTimeParseException;
import java.util.Map;
import java.util.Optional;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/v1/tenant/{tenantId}/insights")
public class IQSController {
    private final RequestRouter requestRouter;
    private final RequestPayloadValidation requestPayloadValidation;
    private final InsightsServiceConfiguration config;
    private final DefaultValues defaultValues;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;
    private final PostgresMetadataService postgresMetadataService;

    @PostMapping("/{pageId}/widget/{widgetId}")
    public Mono<ResponseEntity<Object>> getWidgetData(
            @PathVariable String tenantId,
            @PathVariable String pageId,
            @PathVariable String widgetId,
            @RequestBody RequestPayload payload,
            @RequestHeader MultiValueMap<String, String> headers,
            @RequestParam MultiValueMap<String, String> params) {

        Timer.Sample timer = Timer.start(meterRegistry);

        log.debug("Processing request for page ID: {}, widget ID: {}, with payload: {}", pageId, widgetId, payload.toString());

        // Validate mandatory parameters
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.error("Missing required parameter: tenantID");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing required parameter: tenantID."));
        }
        if (pageId == null || pageId.trim().isEmpty()) {
            log.error("Missing required parameter: pageID");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing required parameter: pageID."));
        }

        defaultValues.setPayloadDefaults(config, payload);

        // Creating a single RequestContext object
        RequestContext requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .pageId(Optional.of(pageId))
                .widgetId(Optional.of(widgetId))
                .requestPayload(Optional.of(payload))
                .headers(Optional.ofNullable(headers))
                .queryParams(Optional.ofNullable(params))
                .build();

        Mono<Metadata> metadataMono = postgresMetadataService.getMetadata(widgetId)
                .doOnNext(m -> log.info("Fetched Metadata for widgetId {}: {}", widgetId, m));


        return metadataMono.flatMap(requestMetadata -> {
            String validationError = requestPayloadValidation.validateRequest(payload, requestMetadata);
            if (!validationError.isEmpty()) {
                return Mono.just(errorResponse(
                        HttpStatus.BAD_REQUEST,
                        "Bad Request",
                        "Invalid request payload: " + validationError
                ));
            }


            return requestRouter.routeRequest(requestMetadata, requestContext)
                    .doOnSuccess(result -> {
                        log.info("Successfully processed request for {}", requestMetadata.getRequestType());
                        metricRecordService.recordTimeAndCountMetrics(
                                meterRegistry, timer, requestMetadata, "success",
                                "Time taken to process request per tenantId, pageId and widgetId", tenantId
                        );
                        metricRecordService.recordSizeMetrics(
                                meterRegistry, result, requestMetadata, tenantId, "success"
                        );
                    })
                    .onErrorResume(error -> {
                        log.error("Error processing request: ", error);
                        metricRecordService.recordTimeAndCountMetrics(
                                meterRegistry, timer, requestMetadata, "error",
                                "Error request duration per tenantId, pageId and widgetId", tenantId
                        );

                        if (error instanceof IllegalArgumentException || error instanceof DateTimeParseException) {
                            ResponseEntity<Object> badRequestResponse = errorResponse(
                                    HttpStatus.BAD_REQUEST, "Bad Request", error.getMessage()
                            );
                            metricRecordService.recordSizeMetrics(
                                    meterRegistry, badRequestResponse, requestMetadata, tenantId, "error"
                            );
                            return Mono.just(badRequestResponse);
                        }

                        ResponseEntity<Object> serverErrorResponse = errorResponse(
                                HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error",
                                "Something went wrong: " + error.getMessage()
                        );
                        metricRecordService.recordSizeMetrics(
                                meterRegistry, serverErrorResponse, requestMetadata, tenantId, "error"
                        );
                        return Mono.just(serverErrorResponse);
                    });
        }).switchIfEmpty(Mono.defer(() -> Mono.just(errorResponse(
                HttpStatus.BAD_REQUEST,
                "Bad Request",
                "Invalid request payload: " + widgetId
        ))));
    }

    private ResponseEntity<Object> errorResponse(HttpStatus status, String error, String message) {
        return ResponseEntity.status(status).body(Map.of("error", error, "message", message));
    }
}

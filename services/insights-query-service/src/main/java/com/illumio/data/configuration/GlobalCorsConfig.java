package com.illumio.data.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class GlobalCorsConfig {

    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**")
                        .allowedOrigins("https://graph.illum.io",
                                "https://insights.redmond.ilabs.io", "https://insights.sunnyvale.ilabs.io",
                                "https://console.sunnyvale.ilabs.io", "https://console.redmond.ilabs.io",
                                "http://localhost:8081", "https://localhost:8443", "https://localhost:8479")
                        .allowedMethods("*")
                        .allowedHeaders("*")
                        .allowCredentials(true)
                        .maxAge(3600);
            }
        };
    }
}

package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;

@Primary
@ConfigurationProperties(prefix = "insights-config")
@Configuration
@Data
public class InsightsServiceConfiguration {
    private final KafkaProducerConfig kafkaProducerConfig = new KafkaProducerConfig();
    private final KustoConfiguration kustoConfig = new KustoConfiguration();
    private final KustoConfiguration kustoInsightsConfig = new KustoConfiguration();
    private final RiskyServiceConfig riskyServiceConfig = new RiskyServiceConfig();
    private final UnencryptedServiceConfig unencryptedServiceConfig = new UnencryptedServiceConfig();
    private final LlmServiceConfig llmServiceConfig = new LlmServiceConfig();
    private final ApiConfig apiConfig = new ApiConfig();
    private final PaginationConfig paginationConfig = new PaginationConfig();
    private final QuarantineConfig quarantineConfig = new QuarantineConfig();
    private final UrlConfig urlConfig = new UrlConfig();
    private final LiquibaseConfig liquibaseConfig = new LiquibaseConfig();
    private final MetadataJdbcConfig metadataJdbcConfig = new MetadataJdbcConfig();

    @Configuration
    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private Boolean isManagedIdentity = false;
        private String saslJaasConfig;
        private String topic;
        private Integer requestTimeoutMs;
        private Integer deliveryTimeoutMs;
        private Integer lingerMs;
        private Integer batchSize;
        private Integer bufferMemory;
        private Integer maxBlockMs;
        private Integer senderMaxAttempts = 10;
        private Duration senderBackoff = Duration.ofSeconds(5);
    }

    @Configuration
    @Getter
    @Setter
    public static class KustoConfiguration {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isManagedIdentity;
        private String managedIdentityClientId;
        private String clusterUri;
        private String database;
        
        private Integer queryMaxRetries = 3;
        private Long queryInitialBackoffSeconds = 1L;
    }

    @Configuration
    @Getter
    @Setter
    public static class RiskyServiceConfig {
        private String riskyServiceFilePath;
    }

    @Configuration
    @Getter
    @Setter
    public static class UnencryptedServiceConfig {
        private String unencryptedServiceFilePath;
    }

    @Configuration
    @Getter
    @Setter
    public static class LlmServiceConfig {
        private String llmServiceFilePath;
    }

    @Configuration
    @Getter
    @Setter
    public static class ApiConfig {
        private String authKey;
    }

    @Configuration
    @Getter
    @Setter
    public static class PaginationConfig {
        private int maxPageSize;
        private int defaultPageSize;
        private int defaultPageNumber;
    }

    @Configuration
    @Getter
    @Setter
    public static class QuarantineConfig {
        private String labelId;
    }

    @Configuration
    @Getter
    @Setter
    public static class UrlConfig {
        private String uiUrl;
    }

    @Configuration
    @Getter
    @Setter
    public static class MetadataJdbcConfig {
        private String driverClassName;
        private String host;
        private Integer port;
        private String database;
        private String username;
        private String password;
    }

    @Configuration
    @Getter
    @Setter
    public static class LiquibaseConfig {
        private String changelogPath;
    }
}

package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.TIME_SERIES;
import static com.illumio.data.model.constants.Fields.COUNT;

import static com.illumio.data.model.constants.RequestMetadata.TOP_SOURCE_TRANSFER;

@Data
@NoArgsConstructor
@Component
public class TopSourceTransferResponse implements ResponseBuilder<TopSourceTransferResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopSourceTransferResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopSourceTransferResponse response = new TopSourceTransferResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(TOP_SOURCE_TRANSFER.getWidgetId());
        response.setTitle("Top Sources With Data Transfer");
        response.setColumns(Arrays.asList(SOURCE_RESOURCE_ID.getFieldKey(), SOURCE_RESOURCE_NAME.getFieldKey(), SOURCE_RESOURCE_CATEGORY.getFieldKey(), SOURCE_RESOURCE_TYPE.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), TIME_SERIES.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SOURCE_RESOURCE_ID.getFieldType(), SOURCE_RESOURCE_NAME.getFieldType(), SOURCE_RESOURCE_CATEGORY.getFieldType(),SOURCE_RESOURCE_TYPE.getFieldType(), AGGREGATE_FIELD.getFieldType(), TIME_SERIES.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SOURCE_RESOURCE_ID.getFieldDisplayName(), SOURCE_RESOURCE_NAME.getFieldDisplayName(), SOURCE_RESOURCE_CATEGORY.getFieldDisplayName(),SOURCE_RESOURCE_TYPE.getFieldDisplayName(),  AGGREGATE_FIELD.getFieldDisplayName(), TIME_SERIES.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        Map<String, Map<String, List<List<Object>>>> groupedData = new HashMap<>();
        while (resultSetTable.next()) {
            String sourceResId = (String) resultSetTable.getObject(SOURCE_RESOURCE_ID.getTableColumnName());
            String sourceResName = (String) resultSetTable.getObject(SOURCE_RESOURCE_NAME.getTableColumnName());
            String sourceResCat = (String) resultSetTable.getObject(SOURCE_RESOURCE_CATEGORY.getTableColumnName());
            String srcResType = (String) resultSetTable.getObject(SOURCE_RESOURCE_TYPE.getTableColumnName());;
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            String timestamp = (String) resultSetTable.getObject(TIME_SERIES.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());

            String compositeKey = sourceResId + "|" + sourceResName + "|" + sourceResCat+ "|" + srcResType;

            groupedData
                    .computeIfAbsent(compositeKey, k -> new HashMap<>())
                    .computeIfAbsent(aggregateField, k -> new ArrayList<>())
                    .add(Arrays.asList(timestamp, count));
        }

        List<List<Object>> formattedData = groupedData.entrySet().stream()
                .flatMap(outer -> {
                    // Split the composite key back into its resource-level parts.
                    String[] parts = outer.getKey().split("\\|", -1);
                    String srcResId = parts[0];
                    String srcResName = parts[1];
                    String srcResCategory = parts[2];
                    String srcResType = parts[3];

                    return outer.getValue().entrySet().stream()
                            .map(inner -> Arrays.asList(
                                    srcResId,
                                    srcResName,
                                    srcResCategory,
                                    srcResType,
                                    inner.getKey(),
                                    inner.getValue()
                            ));
                })
                .collect(Collectors.toList());
        response.setData(formattedData);

        return response;
    }

    @Override
    public TopSourceTransferResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Compresses the response using dictionary/index encoding for maximum token efficiency.
     * This approach creates dictionaries for resource names, categories, and types to avoid repeating strings,
     * and uses numeric arrays for the actual data.
     *
     * Format:
     * {
     *   "t": "20250401-20250604",                 // compact time representation
     *   "res": ["vm-01", "vm-02", ...],           // resource id dictionary
     *   "name": ["Server1", "Server2", ...],       // resource name dictionary
     *   "cat": ["Compute", "Database", ...],       // resource category dictionary
     *   "type": ["VM", "Container", ...],          // resource type dictionary
     *   "agg": ["flows", "bytes", ...],            // aggregate field dictionary
     *   "d": [                                     // data rows with numeric values only
     *     [0, 1, 0, 0, 0, [[ts1,count1],[ts2,count2]]], // resIdx, nameIdx, catIdx, typeIdx, aggIdx, timeSeries
     *     ...
     *   ]
     * }
     * 
     * @return A Map containing the dictionary-compressed response
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();

        // Add compact time representation
        if (currentTimeFrame != null) {
            String currStartTs = currentTimeFrame.getStartTime().replaceAll("[^0-9]", "").substring(0, 8); // Format YYYYMMDD
            String currEndTs = currentTimeFrame.getEndTime().replaceAll("[^0-9]", "").substring(0, 8);
            result.put("ct", currStartTs + "-" + currEndTs);
        }
        if(comparisonTimeFrame != null) {
            String prevStartTs = comparisonTimeFrame.getStartTime().replaceAll("[^0-9]", "").substring(0, 8); // Format YYYYMMDD
            String prevEndTs = comparisonTimeFrame.getEndTime().replaceAll("[^0-9]", "").substring(0, 8);
            result.put("pt", prevStartTs + "-" + prevEndTs);
        }
        
        // Build dictionaries for all string fields
        List<String> resIdDict = new ArrayList<>();
        Map<String, Integer> resIdToIndex = new HashMap<>();
        
        List<String> resNameDict = new ArrayList<>();
        Map<String, Integer> resNameToIndex = new HashMap<>();
        
        List<String> resCatDict = new ArrayList<>();
        Map<String, Integer> resCatToIndex = new HashMap<>();
        
        List<String> resTypeDict = new ArrayList<>();
        Map<String, Integer> resTypeToIndex = new HashMap<>();
        
        List<String> aggFieldDict = new ArrayList<>();
        Map<String, Integer> aggFieldToIndex = new HashMap<>();
        
        List<List<Object>> compressedData = new ArrayList<>();
        
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 6) continue;
                
                String resId = (String) row.get(0);
                String resName = (String) row.get(1);
                String resCat = (String) row.get(2);
                String resType = (String) row.get(3);
                String aggField = (String) row.get(4);
                List<List<Object>> timeSeries = (List<List<Object>>) row.get(5);
                
                // Add to dictionaries if not already present
                if (!resIdToIndex.containsKey(resId)) {
                    resIdToIndex.put(resId, resIdDict.size());
                    resIdDict.add(resId);
                }
                
                if (!resNameToIndex.containsKey(resName)) {
                    resNameToIndex.put(resName, resNameDict.size());
                    resNameDict.add(resName);
                }
                
                if (!resCatToIndex.containsKey(resCat)) {
                    resCatToIndex.put(resCat, resCatDict.size());
                    resCatDict.add(resCat);
                }
                
                if (!resTypeToIndex.containsKey(resType)) {
                    resTypeToIndex.put(resType, resTypeDict.size());
                    resTypeDict.add(resType);
                }
                
                if (!aggFieldToIndex.containsKey(aggField)) {
                    aggFieldToIndex.put(aggField, aggFieldDict.size());
                    aggFieldDict.add(aggField);
                }
                
                // Compress the time series data
                List<List<Object>> compressedTimeSeries = new ArrayList<>();
                boolean hasNonZeroValues = false;
                
                for (List<Object> dataPoint : timeSeries) {
                    String timestamp = (String) dataPoint.get(0);
                    Long count = ((Number)dataPoint.get(1)).longValue();
                    
                    // Keep data points with non-zero values
                    // But also track if we have any non-zero values
                    if (count != null && count > 0) {
                        hasNonZeroValues = true;
                        compressedTimeSeries.add(Arrays.asList(timestamp, count));
                    }
                }
                
                // Skip resources with no time series data at all
                if (compressedTimeSeries.isEmpty() && !hasNonZeroValues) {
                    continue;
                }
                
                // Create data row with indices instead of string values
                List<Object> compressedRow = new ArrayList<>();
                compressedRow.add(resIdToIndex.get(resId));
                compressedRow.add(resNameToIndex.get(resName));
                compressedRow.add(resCatToIndex.get(resCat));
                compressedRow.add(resTypeToIndex.get(resType));
                compressedRow.add(aggFieldToIndex.get(aggField));
                compressedRow.add(compressedTimeSeries);
                
                compressedData.add(compressedRow);
            }
        }
        
        // Add dictionaries and data to result
        result.put("res", resIdDict);
        result.put("name", resNameDict);
        result.put("cat", resCatDict);
        result.put("type", resTypeDict);
        result.put("agg", aggFieldDict);
        result.put("d", compressedData);
        
        // Add pagination info if needed
        if (pagination != null && pagination.getTotalPages() > 1) {
            Map<String, Integer> pg = new HashMap<>();
            pg.put("p", pagination.getPageNumber());
            pg.put("tp", pagination.getTotalPages());
            result.put("pg", pg);
        }
        
        // Add format indicator and title
        result.put("fmt", "dict-v1");
        result.put("w", widgetId);
        result.put("title", title);
        
        return result;
    }
}

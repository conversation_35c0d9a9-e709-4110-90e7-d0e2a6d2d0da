package com.illumio.data.service;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.WidgetBatchRequest;
import com.illumio.data.model.widget.WidgetProcessingRequest;
import com.illumio.data.response.WidgetBatchResponse;
import com.illumio.data.response.WidgetBatchResponse.WidgetResult;
import com.illumio.data.service.batch_widget.WidgetProcessorService;
import com.illumio.data.service.batch_widget.WidgetRequestCreationService;
import com.illumio.data.service.batch_widget.WidgetResultCombinerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for processing batches of widget requests.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WidgetBatchService {
    private final WidgetProcessorService widgetProcessorService;
    private final WidgetRequestCreationService widgetRequestCreationService;
    private final WidgetResultCombinerService widgetResultCombinerService;
    
    // Max number of concurrent widget requests to process
    private static final int MAX_CONCURRENT_WIDGETS = 10;

    /**
     * Process a batch of widget requests
     *
     * @param tenantId      The tenant ID
     * @param batchRequest  The batch request containing widget IDs and payload
     * @param headers       Request headers
     * @param params        Request parameters
     * @return A Mono emitting the batch response
     */
    public Mono<WidgetBatchResponse> processBatchRequest(
            String tenantId,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params) {

        WidgetBatchResponse.WidgetBatchResponseBuilder responseBuilder = WidgetBatchResponse.builder();
        List<String> widgetIds = batchRequest.getWidgetIdList();
        boolean isCompressed = batchRequest.getCompressed() != null && batchRequest.getCompressed();

        if (widgetIds == null || widgetIds.isEmpty()) {
            return Mono.just(WidgetBatchResponse.builder()
                    .results(List.of(WidgetResult.error("batch", "Widget IDs list cannot be empty")))
                    .build());
        }

        // Expand the widgetIds list to include widgets that need to be processed with different directions/categories
        List<WidgetProcessingRequest> processingRequests = widgetRequestCreationService.createProcessingRequests(widgetIds);
        
        log.debug("Processing {} widget requests (including direction/category variants)", processingRequests.size());

        // Process each widget in parallel with bounded concurrency
        return Flux.fromIterable(processingRequests)
                .flatMap(request -> 
                    widgetProcessorService.processWidgetRequest(
                        tenantId, 
                        request, 
                        batchRequest.getCurrentTimeFrame(),
                        batchRequest.getComparisonTimeFrame(),
                        headers, 
                        params
                    ),
                    MAX_CONCURRENT_WIDGETS
                )
                .collectList()
                .map(results -> {
                    // Combine direction-specific and category-specific results for the same widget ID
                    List<WidgetResult> combinedResults = widgetResultCombinerService.combineResults(results);
                    
                    // Apply compression if requested
                    if (isCompressed) {
                        combinedResults = combinedResults.stream()
                                .map(this::compressWidgetResultIfNeeded)
                                .collect(Collectors.toList());
                        log.debug("Compressed responses for {} widgets", combinedResults.size());
                    }
                    
                    return responseBuilder.results(combinedResults).build();
                });
    }
    
    /**
     * Compresses a widget result if it's successful and contains data that is compressible.
     * For error results, or results where the data cannot be compressed, the original result is returned.
     * 
     * @param result The WidgetResult to potentially compress
     * @return A new WidgetResult with compressed data, or the original if compression is not applicable
     */
    private WidgetResult compressWidgetResultIfNeeded(WidgetResult result) {
        if (!"success".equals(result.getStatus()) || result.getData() == null) {
            return result;
        }
        
        try {
            if (result.getData() instanceof ResponseBuilder) {
                ResponseBuilder<?> responseBuilder = (ResponseBuilder<?>) result.getData();
                return WidgetResult.builder()
                        .widgetId(result.getWidgetId())
                        .status(result.getStatus())
                        .data(responseBuilder.compress())
                        .build();
            } 
            return result;
        } catch (Exception e) {
            log.warn("Failed to compress widget result for ID {}: {}", result.getWidgetId(), e.getMessage());
            // Return the original result if compression fails
            return result;
        }
    }
} 
package com.illumio.data.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.SeverityLevel;
import com.illumio.data.service.RiskyServiceInfo;
import com.illumio.data.service.UnencryptedServiceInfo;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.SERVICE;
import static com.illumio.data.model.constants.Fields.SEVERITY;
import static com.illumio.data.model.constants.Fields.TOTAL_ROWS;
import static com.illumio.data.model.constants.RequestMetadata.UNENCRYPTED_SERVICES;

@JsonIgnoreProperties({"unencryptedServiceInfo"})
@Data
@Component
public class UnencryptedServiceResponse implements ResponseBuilder<UnencryptedServiceResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    private final UnencryptedServiceInfo unencryptedServiceInfo;

    public UnencryptedServiceResponse(UnencryptedServiceInfo unencryptedServiceInfo) {
        this.unencryptedServiceInfo = unencryptedServiceInfo;
    }

    @Override
    public UnencryptedServiceResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        UnencryptedServiceResponse response = this;

        //TODO: This should come from API - but for the time being, we are defaulting this to true
        Boolean showAllData = true;

        //Set Widget Specific Metadata
        response.setWidgetId(UNENCRYPTED_SERVICES.getWidgetId());
        response.setTitle("Unencrypted Services in ICT Systems");
        response.setColumns(Arrays.asList(SERVICE.getFieldKey(), PORT.getFieldKey(), PROTOCOL.getFieldKey(), PREVIOUS_AGGREGATED_FLOWS.getFieldKey(), AGGREGATED_FLOWS.getFieldKey(), PREVIOUS_AGGREGATED_BYTES.getFieldKey(), AGGREGATED_BYTES.getFieldKey(), SEVERITY.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SERVICE.getFieldType(), PORT.getFieldType(), PROTOCOL.getFieldType(), PREVIOUS_AGGREGATED_FLOWS.getFieldType(), AGGREGATED_FLOWS.getFieldType(), PREVIOUS_AGGREGATED_BYTES.getFieldType(), AGGREGATED_BYTES.getFieldType(), SEVERITY.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SERVICE.getFieldDisplayName(), PORT.getFieldDisplayName(), PROTOCOL.getFieldDisplayName(), PREVIOUS_AGGREGATED_FLOWS.getFieldDisplayName(), AGGREGATED_FLOWS.getFieldDisplayName(), PREVIOUS_AGGREGATED_BYTES.getFieldDisplayName(), AGGREGATED_BYTES.getFieldDisplayName(), SEVERITY.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        // Track existing (port, protocol) pairs from resultSetTable
        Set<UnencryptedServiceInfo.PortProtocolPair> existingPairs = new HashSet<>();
        Set<UnencryptedServiceInfo.PortProtocolPair> addedPairs = new HashSet<>();

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            Integer port = (Integer) resultSetTable.getObject(PORT.getTableColumnName());
            String protocol = (String) resultSetTable.getObject(PROTOCOL.getTableColumnName());
            String service = Optional.ofNullable(port)
                    .flatMap(p -> Optional.ofNullable(protocol)
                            .flatMap(pr -> Optional.ofNullable(
                                            unencryptedServiceInfo.getPortProtoToServiceInfoMap().get(new UnencryptedServiceInfo.PortProtocolPair(p, pr.toUpperCase())))
                                    .map(UnencryptedServiceInfo.ServiceInfo::getService)))
                    .orElse("UNKNOWN");
            Long flowCount = getLongValue(resultSetTable, AGGREGATED_FLOWS.getTableColumnName());
            Long bytes = getLongValue(resultSetTable, AGGREGATED_BYTES.getTableColumnName());
            Long prevFlowCount = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_FLOWS.getTableColumnName());
            Long prevBytes = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_BYTES.getTableColumnName());

            UnencryptedServiceInfo.PortProtocolPair pair = new UnencryptedServiceInfo.PortProtocolPair(port, protocol.toUpperCase());
            existingPairs.add(pair);

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(service);
            dataRow.add(port);
            dataRow.add(protocol);
            dataRow.add(prevFlowCount);
            dataRow.add(flowCount);
            dataRow.add(prevBytes);
            dataRow.add(bytes);
            dataRow.add(Optional.ofNullable(port)
                    .flatMap(p -> Optional.ofNullable(protocol)
                            .flatMap(pr -> Optional.ofNullable(
                                            unencryptedServiceInfo.getPortProtoToServiceInfoMap().get(new UnencryptedServiceInfo.PortProtocolPair(p, pr.toUpperCase())))
                                    .map(UnencryptedServiceInfo.ServiceInfo::getSeverity)))
                    .map(SeverityLevel::fromString)
                    .orElse(-1)); // Default value when severity is unknown
            data.add(dataRow);

            //Set total pages = (int) ceil(total_rows / rows per page). Set this only once in the response.
            if (resultSetTable.isLast()) {
                response.pagination.setTotalPages((int) Math.ceil((double)resultSetTable.getInt(TOTAL_ROWS.getTableColumnName()) / response.pagination.getRowLimit()));
            }
        }

        var prevFlowCount = 0;
        var flowCount = 0;
        var prevBytes = 0;
        var bytes = 0;
        if(showAllData){
            // Adding missing service-port-protocol combinations
            for (Map.Entry<UnencryptedServiceInfo.PortProtocolPair, UnencryptedServiceInfo.ServiceInfo> entry : unencryptedServiceInfo.getPortProtoToServiceInfoMap().entrySet()) {
                UnencryptedServiceInfo.PortProtocolPair pair = entry.getKey();
                UnencryptedServiceInfo.ServiceInfo serviceInfo = entry.getValue();
                String service = serviceInfo.getService();
                int severity = SeverityLevel.fromString(serviceInfo.getSeverity());
                if (!existingPairs.contains(pair) && !addedPairs.contains(pair)) {
                    List<Object> missingDataRow = new ArrayList<>();
                    missingDataRow.add(service);
                    missingDataRow.add(pair.getPort());
                    missingDataRow.add(pair.getProtocol());
                    missingDataRow.add(prevFlowCount);
                    missingDataRow.add(flowCount);
                    missingDataRow.add(prevBytes);
                    missingDataRow.add(bytes);
                    missingDataRow.add(severity);
                    data.add(missingDataRow);
                    addedPairs.add(pair);
                }
            }
        }

        response.setData(data);
        return response;
    }

    @Override
    public UnencryptedServiceResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
}

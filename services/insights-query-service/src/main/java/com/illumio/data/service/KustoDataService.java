package com.illumio.data.service;

import com.illumio.data.KustoQueryClient;
import com.illumio.data.datavisitor.KustoEntity;
import com.illumio.data.datavisitor.KustoQueryVisitor;
import com.illumio.data.model.RequestContext;
import com.illumio.data.builder.ResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import static com.illumio.data.model.constants.RequestMetadata.DESTINATION_ROLE_LEVEL_TRAFFIC;
import static com.illumio.data.model.constants.RequestMetadata.DORA_CRITICAL_ICT;
import static com.illumio.data.model.constants.RequestMetadata.DORA_TOP_ICT;
import static com.illumio.data.model.constants.RequestMetadata.RESOURCE_INSIGHTS;
import static com.illumio.data.model.constants.RequestMetadata.RISKY_SERVICE_TRAFFIC;
import static com.illumio.data.model.constants.RequestMetadata.RISKY_SERVICE_TRAFFIC_INSIGHTS_HUB;
import static com.illumio.data.model.constants.RequestMetadata.THIRD_PARTY_DEPENDENCY_INBOUND;
import static com.illumio.data.model.constants.RequestMetadata.THIRD_PARTY_DEPENDENCY_OUTBOUND;
import static com.illumio.data.model.constants.RequestMetadata.THREAT_MAP;
import static com.illumio.data.model.constants.RequestMetadata.TOP_CATEGORY_WITH_MALICIOUS_IP;
import static com.illumio.data.model.constants.RequestMetadata.TOP_CROSS_REGION_TRAFFIC;
import static com.illumio.data.model.constants.RequestMetadata.TOP_DESTINATION_ROLES;
import static com.illumio.data.model.constants.RequestMetadata.TOP_DESTINATION_ROLES_INSIGHTS_HUB;
import static com.illumio.data.model.constants.RequestMetadata.TOP_MALICIOUS_IPS;
import static com.illumio.data.model.constants.RequestMetadata.TOP_MALICIOUS_IPS_INSIGHTS_HUB;
import static com.illumio.data.model.constants.RequestMetadata.TOP_REGION_TO_COUNTRY_TRAFFIC;
import static com.illumio.data.model.constants.RequestMetadata.TOP_ROLES;
import static com.illumio.data.model.constants.RequestMetadata.TOP_SERVICES;
import static com.illumio.data.model.constants.RequestMetadata.TOP_SOURCE_TRANSFER_INSIGHTS_HUB;
import static com.illumio.data.model.constants.RequestMetadata.TOP_WORKLOADS;
import static com.illumio.data.model.constants.RequestMetadata.UNENCRYPTED_SERVICES;
import static com.illumio.data.model.constants.RequestMetadata.ZONE_LEVEL_TRAFFIC;
import static com.illumio.data.model.constants.RequestMetadata.EXTERNAL_DESTINATION_CATEGORY;
import static com.illumio.data.model.constants.RequestMetadata.EXTERNAL_GEO_TRANSFER;
import static com.illumio.data.model.constants.RequestMetadata.EXTERNAL_SERVICE_TRANSFER;
import static com.illumio.data.model.constants.RequestMetadata.TOP_SOURCE_TRANSFER;
import static com.illumio.data.model.constants.RequestMetadata.TOP_SOURCE_ROLE_TRANSFER;
import static com.illumio.data.model.constants.RequestMetadata.LLM_IN_USE;
import static com.illumio.data.model.constants.RequestMetadata.TOP_CATEGORY_WITH_LLM;
import static com.illumio.data.model.constants.RequestMetadata.TOP_SOURCES_WITH_LLM;
import static com.illumio.data.model.constants.RequestMetadata.RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS;
import static com.illumio.data.model.constants.RequestMetadata.MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS;
import static com.illumio.data.model.constants.RequestMetadata.EXTERNAL_DATA_TRANSFER_RESOURCE_INSIGHTS;
import static com.illumio.data.model.constants.RequestMetadata.RISKY_SERVICES_TRAFFIC_RESOURCE_INSIGHTS;

@Service
@Slf4j
public class KustoDataService implements DataService<KustoEntity> {

    private final KustoQueryClient kustoQueryClient;

    @Autowired
    public KustoDataService(KustoQueryClient kustoQueryClient) {
        this.kustoQueryClient = kustoQueryClient;
    }

    @Override
    public <R> Mono<R> getRiskyServiceTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, RISKY_SERVICE_TRAFFIC, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getZoneLevelTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, ZONE_LEVEL_TRAFFIC, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopDestinationRoles(RequestContext requestContext,ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_DESTINATION_ROLES, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopWorkloads(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_WORKLOADS, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getDestRoleTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, DESTINATION_ROLE_LEVEL_TRAFFIC, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, RESOURCE_INSIGHTS, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopMaliciousIps(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_MALICIOUS_IPS, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getThreatMap(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, THREAT_MAP, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopCategoryWithMaliciousIp(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_CATEGORY_WITH_MALICIOUS_IP, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopRoles(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_ROLES, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopServices(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_SERVICES, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getExternalDestCategoryTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, EXTERNAL_DESTINATION_CATEGORY, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopSourceRoleTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_SOURCE_ROLE_TRANSFER, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getExternalGeoTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, EXTERNAL_GEO_TRANSFER, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopSourceTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_SOURCE_TRANSFER, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getExternalServiceTransfer(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, EXTERNAL_SERVICE_TRANSFER, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getLlmInUse(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, LLM_IN_USE, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopCategoryWithLlm(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_CATEGORY_WITH_LLM, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopSourcesWithLlm(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_SOURCES_WITH_LLM, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getThirdPartyInbound(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, THIRD_PARTY_DEPENDENCY_INBOUND, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getThirdPartyOutbound(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, THIRD_PARTY_DEPENDENCY_OUTBOUND, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopSourceTransferInsightsHub(RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_SOURCE_TRANSFER_INSIGHTS_HUB, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopMaliciousIpsInsightsHub(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_MALICIOUS_IPS_INSIGHTS_HUB, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopDestinationRolesInsightsHub(RequestContext requestContext,ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_DESTINATION_ROLES_INSIGHTS_HUB, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getRiskyServiceTrafficInsightsHub(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, RISKY_SERVICE_TRAFFIC_INSIGHTS_HUB, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopCrossRegionTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_CROSS_REGION_TRAFFIC, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getTopRegionToCountryTraffic(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, TOP_REGION_TO_COUNTRY_TRAFFIC, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getRiskyTrafficByRolesResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getMaliciousIpTrafficResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getExternalDataTransferResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, EXTERNAL_DATA_TRANSFER_RESOURCE_INSIGHTS, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getUnencryptedServices(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, UNENCRYPTED_SERVICES, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getDoraTopIct(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, DORA_TOP_ICT, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getDoraCriticalIct(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, DORA_CRITICAL_ICT, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }

    @Override
    public <R> Mono<R> getRiskyServicesTrafficResourceInsights(RequestContext requestContext, ResponseBuilder<R> responseBuilder){
        KustoEntity entity = new KustoEntity();
        KustoQueryVisitor client = new KustoQueryVisitor(kustoQueryClient);
        return client.visit(entity, RISKY_SERVICES_TRAFFIC_RESOURCE_INSIGHTS, requestContext, responseBuilder)
                .doOnNext(result -> log.debug("Result obtained: {}", result));
    }
}

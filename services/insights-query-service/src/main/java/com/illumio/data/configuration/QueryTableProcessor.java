package com.illumio.data.configuration;

import com.illumio.data.model.constants.RequestMetadata;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import org.springframework.stereotype.Component;

/**
 * Configuration class for query table names and logic to determine which table to query.
 */

@Component
@Getter
@Setter
public class QueryTableProcessor {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

    public Mono<String> determineQueryTable(String start, String end, RequestMetadata metadata) {

        return Mono.fromSupplier(() -> {
            LocalDateTime startTime = LocalDateTime.parse(start, DATE_TIME_FORMATTER);
            LocalDateTime endTime = LocalDateTime.parse(end, DATE_TIME_FORMATTER);
            long days = ChronoUnit.DAYS.between(startTime, endTime);
            if (days < 1 || (days==1 && isInPastDayRange(startTime, endTime, metadata))) {
                return "Insights_" + metadata.getTableName() + "_Hourly";
            }
            if (isFullMonthRange(startTime, endTime)) {
                return "Insights_" + metadata.getTableName() + "_Monthly";
            }
            if (days % 7 == 0) {
                return "Insights_" + metadata.getTableName() + "_Weekly";
            }
            // Any non-standard range → custom → default to daily
            return "Insights_" + metadata.getTableName() + "_Daily";
        });

    }

    /**
     * Checks if the time range spans exactly one calendar month.
     * Works for:
     * - Jan 31 → Feb 28
     * - Feb 1 → Mar 1
     * - Mar 15 → Apr 15
     *  Multiple full calendar months
     */
    private boolean isFullMonthRange(LocalDateTime start, LocalDateTime end) {
        // Updating timeframe with floored timestamp (00:00:00Z).
        start = start.withHour(0).withMinute(0).withSecond(0).withNano(0);
        end = end.withHour(0).withMinute(0).withSecond(0).withNano(0);

        while (start.isBefore(end)) {
            start = start.plusMonths(1);
            if (start.isEqual(end)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the time range falls in the past 24 hours
     */
    public Boolean isInPastDayRange(LocalDateTime start, LocalDateTime end, RequestMetadata metadata){
        LocalDateTime truncatedStart = start.truncatedTo(ChronoUnit.HOURS);
        LocalDateTime truncatedEnd = end.truncatedTo(ChronoUnit.HOURS);
        LocalDateTime truncatedNow = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS);
        LocalDateTime twentyFourHoursAgo = truncatedNow.minusHours(24);

        // check if the TimeRange falls into past 24 hours - route to hourly table
        return !truncatedStart.isBefore(twentyFourHoursAgo) && !truncatedEnd.isAfter(truncatedNow);
    }
}

package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.DESTINATION_COUNTRY;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;

import static com.illumio.data.model.constants.RequestMetadata.EXTERNAL_GEO_TRANSFER;

@Data
@NoArgsConstructor
@Component
public class ExternalGeoTransferResponse implements ResponseBuilder<ExternalGeoTransferResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public ExternalGeoTransferResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        ExternalGeoTransferResponse response = new ExternalGeoTransferResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(EXTERNAL_GEO_TRANSFER.getWidgetId());
        response.setTitle("Data Transfer To External Geos");
        response.setColumns(Arrays.asList(DESTINATION_COUNTRY.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), COUNT.getFieldKey()));
        response.setColumnTypes(Arrays.asList(DESTINATION_COUNTRY.getFieldType(), AGGREGATE_FIELD.getFieldType(), PREVIOUS_COUNT.getFieldType(), COUNT.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(DESTINATION_COUNTRY.getFieldDisplayName(),  AGGREGATE_FIELD.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), COUNT.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String destinationCountry = (String) resultSetTable.getObject(DESTINATION_COUNTRY.getTableColumnName());
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            Long prevValue = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            Long currentValue = getLongValue(resultSetTable, COUNT.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(destinationCountry);
            dataRow.add(aggregateField);
            dataRow.add(prevValue);
            dataRow.add(currentValue);
            data.add(dataRow);
        }
        response.setData(data);

        return response;
    }

    @Override
    public ExternalGeoTransferResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Compresses the response using dictionary/index encoding for maximum token efficiency.
     * This approach creates dictionaries for country names and aggregate fields to avoid repeating strings,
     * and uses numeric arrays for the actual data.
     *
     * Format:
     * {
     *   "t": "20250401-20250604",                 // compact time representation
     *   "country": ["US", "CN", "DE", ...],       // country dictionary
     *   "agg": ["flows", "bytes", ...],           // aggregate field dictionary
     *   "d": [                                    // data rows with numeric values only
     *     [0, 1, 5072, 4210],                     // countryIdx, aggFieldIdx, prevCount, count
     *     ...
     *   ]
     * }
     * 
     * @return A Map containing the dictionary-compressed response
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();

        // Add compact time representation
        if (currentTimeFrame != null) {
            String currStartTs = currentTimeFrame.getStartTime().replaceAll("[^0-9]", "").substring(0, 8); // Format YYYYMMDD
            String currEndTs = currentTimeFrame.getEndTime().replaceAll("[^0-9]", "").substring(0, 8);
            result.put("ct", currStartTs + "-" + currEndTs);
        }
        if(comparisonTimeFrame != null) {
            String prevStartTs = comparisonTimeFrame.getStartTime().replaceAll("[^0-9]", "").substring(0, 8); // Format YYYYMMDD
            String prevEndTs = comparisonTimeFrame.getEndTime().replaceAll("[^0-9]", "").substring(0, 8);
            result.put("pt", prevStartTs + "-" + prevEndTs);
        }
        
        // Build dictionaries for country names and aggregate fields
        List<String> countryDict = new ArrayList<>();
        Map<String, Integer> countryToIndex = new HashMap<>();
        
        List<String> aggFieldDict = new ArrayList<>();
        Map<String, Integer> aggFieldToIndex = new HashMap<>();
        
        List<List<Object>> compressedData = new ArrayList<>();
        
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 4) continue;
                
                String country = (String) row.get(0);
                String aggField = (String) row.get(1);
                Long prevCount = ((Number)row.get(2)).longValue();
                Long count = ((Number)row.get(3)).longValue();
                
                // Only skip countries with ALL zero/null values
                // Keep countries with ANY non-zero value
                if ((prevCount == null || prevCount == 0L) && 
                    (count == null || count == 0L)) {
                    continue;
                }
                
                // Add strings to dictionaries if not already present
                if (!countryToIndex.containsKey(country)) {
                    countryToIndex.put(country, countryDict.size());
                    countryDict.add(country);
                }
                
                if (!aggFieldToIndex.containsKey(aggField)) {
                    aggFieldToIndex.put(aggField, aggFieldDict.size());
                    aggFieldDict.add(aggField);
                }
                
                // Create data row with indices instead of string values
                List<Object> compressedRow = new ArrayList<>();
                compressedRow.add(countryToIndex.get(country));
                compressedRow.add(aggFieldToIndex.get(aggField));
                compressedRow.add(prevCount);
                compressedRow.add(count);
                
                compressedData.add(compressedRow);
            }
        }
        
        // Add dictionaries and data to result
        result.put("country", countryDict);
        result.put("agg", aggFieldDict);
        result.put("d", compressedData);
        
        // Add pagination info if needed
        if (pagination != null && pagination.getTotalPages() > 1) {
            Map<String, Integer> pg = new HashMap<>();
            pg.put("p", pagination.getPageNumber());
            pg.put("tp", pagination.getTotalPages());
            result.put("pg", pg);
        }
        
        // Add format indicator and title
        result.put("fmt", "dict-v1");
        result.put("w", widgetId);
        result.put("title", title);
        
        return result;
    }
}

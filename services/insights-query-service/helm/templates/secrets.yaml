apiVersion: v1
kind: Secret
metadata:
  name: {{ include "iqs.fullname" . }}-env-secrets
  labels:
    {{- include "iqs.labels" . | nindent 4 }}
type: Opaque
stringData:
  INSIGHTSCONFIG_KUSTOCONFIG_AZURECLIENTID: {{ .Values.insightsConfig.kustoConfig.azureClientId }}
  INSIGHTSCONFIG_KUSTOCONFIG_AZURECLIENSECRET: {{ .Values.insightsConfig.kustoConfig.azureClientSecret }}
  INSIGHTSCONFIG_KUSTOCONFIG_AZURETENANTID: {{ .Values.insightsConfig.kustoConfig.azureTenantId }}

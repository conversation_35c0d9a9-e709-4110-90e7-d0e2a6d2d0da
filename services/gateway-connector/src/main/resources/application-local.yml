server:
  port: 8080

jwt:
  secret: "Testing-spring-cloud-gateway-connectivity-1996"

cache:
  maximum-size: 1000
  expire-after-write: 10


spring:
  application:
    name: gateway-connector

  cloud:
    gateway:
      routes:
        # Adding Core filter
        - id: core-service
          uri: http://localhost:8080  # This gets overridden
          predicates:
            - Path=/core/**
          filters:
            - name: PermissionFilter
            - name: CoreFilter
              args:
                message: "Routing core"
            - StripPrefix=1

        - id: asgard-service
          uri: http://localhost:8080
          predicates:
            - Path=/trpc/**
          filters:
            - name: AsgardFilter
              args:
                message : "Inside trpc route"
            - StripPrefix=1

        - id: asgard-service
          uri: http://localhost:8080
          predicates:
            - Host=*.console.illumio.com
          filters:
            name: AsgardFilter
            args:
              message: "Inside trpc route"

      forwarded:
        enabled: true

grpc:
  authservice:
    host: census-grpc.console.sunnyvale.ilabs.io
    port: 443

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true


logging:
  level:
    root: INFO
    org.springframework.web.reactive.function.client.ExchangeFunctions: DEBUG
    org.springframework.cloud.gateway.filter: DEBUG
    org.springframework.cloud.gateway: TRACE
    reactor.netty.http.server: DEBUG
    reactor.netty: DEBUG
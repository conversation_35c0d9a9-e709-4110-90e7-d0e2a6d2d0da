server:
  port: 8080

JWT_SECRET: "VGVzdGluZy1zcHJpbmctY2xvdWQtZ2F0ZXdheS1jb25uZWN0aXZpdHktMTk5Ng=="

cache:
  maximum-size: 1000
  expire-after-write: 10


spring:
  application:
    name: gateway-connector

  cloud:
    gateway:
      routes:
        # Adding Core filter
        - id: core-service
          uri: http://localhost:8080  # This gets overridden
          predicates:
            - Path=/api/core/**
          filters:
            - name: PermissionFilter
            - name: CoreFilter
              args:
                message: "Routing core"
            - StripPrefix=2

        - id: asgard-service
          uri: http://localhost:8080
          predicates:
            - Path=/api/asgard/**
          filters:
            - name: AsgardFilter
              args:
                message : "Inside trpc route"
            - StripPrefix=2


        - id: Integrations
          uri: http://localhost:8080
          predicates:
            - Path=/api/integrations/**
          filters:
            - name: IntegrationsFilter
              args:
                message : "PlaceHolder for Integrations"
            - StripPrefix=2

        - id: MachineLearning
          uri: http://localhost:8080
          predicates:
            - Path=/api/ML/**
          filters:
            - name: MachineLearningFilter
              args:
                message : "PlaceHolder for ML"
            - StripPrefix=2

        - id: CloudSecure
          uri: http://localhost:8080
          predicates:
            - Path=/api/cs/**
          filters:
            - name: CloudSecureFilter
              args:
                message: "PlaceHolder for CloudSecure"
            - StripPrefix=2

        - id: insights-search-service
          uri: https://insights.sunnyvale.ilabs.io
          predicates:
            - Path=/api/v1/search/tenant/**
          filters:
            - name: PermissionFilter
            - name: InsightsSearchFilter
              args:
                message: "Filter for Insights search Service"
            - RewritePath=/api/v1/search/tenant/(?<remaining>.*), /api/v1/search/tenant/${remaining}


      forwarded:
        enabled: true

grpc:
  authservice:
    host: census-grpc.console.sunnyvale.ilabs.io
    port: 443

management:
  endpoints:
    web:
      exposure:
        include: health, info
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true

logging:
  level:
    root: INFO
    org.springframework.web.reactive.function.client.ExchangeFunctions: DEBUG
    org.springframework.cloud.gateway.filter: DEBUG
    org.springframework.cloud.gateway: TRACE
    reactor.netty.http.server: DEBUG
    reactor.netty: DEBUG
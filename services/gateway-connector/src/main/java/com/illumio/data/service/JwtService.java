package com.illumio.data.service;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Base64;
import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class JwtService {

    @Value("${JWT_SECRET}")
    private String secretKey;

    public String generatePermissionsJwt(Map<String, Object> permissions, int timeoutMinutes) {
        Duration duration = Duration.ofMinutes(timeoutMinutes);
        long expirationMillis = System.currentTimeMillis() + duration.toMillis();
        return Jwts.builder()
                .setClaims(permissions)
                .setIssuedAt(new Date())
                .setExpiration(new Date(expirationMillis))
                .signWith(SignatureAlgorithm.HS256, Base64.getDecoder().decode(secretKey))
                .compact();
    }
}


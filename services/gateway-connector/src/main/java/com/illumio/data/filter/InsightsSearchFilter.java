package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import com.illumio.data.service.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class InsightsSearchFilter extends AbstractGatewayFilterFactory<InsightsSearchFilter.Config> {

    private final JwtService jwtService;

    public InsightsSearchFilter(JwtService jwtService) {
        super(Config.class);
        this.jwtService = jwtService;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return ((exchange, chain) -> {
            log.info("InsightsQueryFilter : {}", config.getMessage() );
            GetUserSessionPermissionsResponse grpcResponse = exchange.getAttribute("grpcSession");
            if (grpcResponse == null) {
                log.warn("gRPC session not found in request attributes");
                return chain.filter(exchange);
            }
            String tenant_id = grpcResponse.getCloudsecure().getTenantId();
            Map<String, Object> jwtClaims = new HashMap<>();
            jwtClaims.put("tenant_id", tenant_id);
            String claims = jwtService.generatePermissionsJwt(jwtClaims, 10);

            ServerHttpRequest mutatedRequest = exchange.getRequest().mutate()
                    .header("X-GW-Insights", claims)
                    .build();
            ServerWebExchange mutatedExchange = exchange.mutate()
                    .request(mutatedRequest)
                    .build();
            return chain.filter(mutatedExchange);
        });
    }

    public static class Config {
        private String message;
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}

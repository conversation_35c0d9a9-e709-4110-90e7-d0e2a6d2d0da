package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.util.JsonFormat;
import com.illumio.data.config.JwtClaimsMapper;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import static com.illumio.data.constants.Constants.PERMISSIONS_SIZE;
import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;
import static com.illumio.data.constants.Constants.TIMEZONE;

@Slf4j
@Component
public class CoreFilter extends AbstractGatewayFilterFactory<CoreFilter.Config> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final JwtService jwtService;

    private final Counter largePermissionsCounter;

    public CoreFilter(JwtService jwtService, MeterRegistry meterRegistry) {
        super(Config.class);
        this.jwtService = jwtService;
        this.largePermissionsCounter = meterRegistry.counter("gateway.large_permissions_counter");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            GetUserSessionPermissionsResponse grpcResponse = exchange.getAttribute("grpcSession");
            String csi_token = exchange.getRequest().getHeaders().getFirst("X-csi");
            if (grpcResponse == null) {
                log.warn("gRPC session not found in request attributes");
                return chain.filter(exchange);
            }

            try {
                // === Extract target FQDN and port from gRPC response ===
                String fqdn = grpcResponse.getCore().getPceFqdn();
                String host = fqdn.split(":")[0];
                String port = fqdn.split(":")[1];

                // === Rewrite URI to new FQDN ===
                URI originalUri = exchange.getRequest().getURI();
                URI newUri = UriComponentsBuilder.fromUri(originalUri)
                        .scheme("https")
                        .host(host)
                        .port(port)
                        .build(true)
                        .toUri();

                // === Clone headers from original request ===
                HttpHeaders newHeaders = new HttpHeaders();
                newHeaders.putAll(exchange.getRequest().getHeaders());

                String last_updated_at = grpcResponse.getLastUpdatedAt();

                /*
                Cleaning and
                 */
                String cleaned = last_updated_at.replaceAll(" \\(.*\\)$", "");
                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(
                        "EEE MMM dd yyyy HH:mm:ss 'GMT'Z", Locale.ENGLISH);
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(cleaned, inputFormatter);
                ZonedDateTime pacificTime = zonedDateTime.withZoneSameInstant(ZoneId.of(TIMEZONE));
                // Convert to ISO 8601 string
                String iso8601_format = pacificTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);

                // Copy CSI token if present
                String csiToken = exchange.getRequest().getHeaders().getFirst("X-CSI-Token");
                if (csiToken != null) {
                    newHeaders.set("X-Csi", csiToken);
                }

                String json = JsonFormat.printer()
                        .preservingProtoFieldNames()
                        .print(grpcResponse);

                boolean permissions_excluded = false;
                Map<String, Object> jwtClaims;
                if (json.getBytes(StandardCharsets.UTF_8).length > PERMISSIONS_SIZE) {
                    largePermissionsCounter.increment();
                    log.warn("response > 4KB, skipping JWE and setting X-Permissions-Offloaded header");
                    //TODO Add metrics to check how many requests are >4kb
                    jwtClaims = new HashMap<>(JwtClaimsMapper.mapCoreToJwtClaim(grpcResponse));
                    log.info("response > 4kb {}", jwtClaims);
                    permissions_excluded = true;
                } else {
                    // Convert gRPC message to JSON and encrypt to JWE
                    jwtClaims = new HashMap<>(objectMapper.readValue(json,
                            new com.fasterxml.jackson.core.type.TypeReference<HashMap<String,Object>>() {}
                    ));
                }
                jwtClaims.put("csi_token", csi_token);
                jwtClaims.put("permissions_excluded", permissions_excluded);
                jwtClaims.put("last_updated_at", iso8601_format);


                log.info("Sending response to Core : {}", jwtClaims);

                // === Mutate request with new URI and headers ===
                String jwt = jwtService.generatePermissionsJwt(jwtClaims, grpcResponse.getCore().getSessionTimeoutMinutes());


                newHeaders.set(GATEWAY_TOKEN, Base64.getEncoder().withoutPadding().encodeToString(
                        jwt.getBytes(StandardCharsets.UTF_8)
                ));

                ServerHttpRequestDecorator mutatedRequest = new ServerHttpRequestDecorator(exchange.getRequest().mutate()
                        .uri(newUri)
                        .headers(httpHeaders -> {
                            httpHeaders.clear();
                            httpHeaders.putAll(newHeaders);
                        })
                        .build());

                ServerWebExchange mutatedExchange = exchange.mutate()
                        .request(mutatedRequest)
                        .build();

                log.debug("Forwarding to URI [{}] with headers: {}", newUri, newHeaders.keySet());
                return chain.filter(mutatedExchange);

            } catch (Exception e) {
                log.error("CoreFilter failed", e);
                return Mono.error(e);
            }
        };

    }

    public static class Config {
        private String message;
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}

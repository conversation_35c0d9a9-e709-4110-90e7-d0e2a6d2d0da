package com.illumio.data.cache;

import authservice.GetUserSessionPermissionsResponse;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {

    @Value("${cache.maximum-size}")
    private int maximumSize;

    @Value("${cache.expire-after-write}")
    private int expireAfterWrite;

    @Bean
    public Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache() {
        return Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterWrite(expireAfterWrite, TimeUnit.MINUTES)
                .build();
    }

}

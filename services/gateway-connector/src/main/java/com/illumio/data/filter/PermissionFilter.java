package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsRequest;
import authservice.GetUserSessionPermissionsResponse;
import authservice.ReactorAuthServiceGrpc.ReactorAuthServiceStub;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PermissionFilter extends AbstractGatewayFilterFactory<PermissionFilter.Config> {

    private final Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache;
    private final ReactorAuthServiceStub authServiceStub;

    public static class Config {
        // Specify Filter config when needed
    }

    @Autowired
    public PermissionFilter(
            Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache,
            ReactorAuthServiceStub authServiceStub
    ) {
        super(Config.class);
        this.csiPermissionCache = csiPermissionCache;
        this.authServiceStub = authServiceStub;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            log.info(">> PermissionFilter invoked. Path = {}", exchange.getRequest().getPath());
            String csiToken = exchange.getRequest().getHeaders().getFirst("X-csi");

            if (csiToken == null || csiToken.isEmpty()) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                return exchange.getResponse().setComplete();
            }

            log.info(">> csiToken: {}", csiToken);

            // Check if permissions already present in the attribute
            GetUserSessionPermissionsResponse existingSession = exchange.getAttribute("grpcSession");
            if (existingSession != null) {
                log.info("grpcSession already present in exchange. Skipping PermissionFilter logic.");
                return chain.filter(exchange);
            }

            String xff = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");

            GetUserSessionPermissionsRequest request = GetUserSessionPermissionsRequest.newBuilder()
                    .setCsi(csiToken)
                    .setXff(xff)
                    .setReferrer("PermissionFilter")
                    .build();

            GetUserSessionPermissionsResponse cachedUserSession = csiPermissionCache.getIfPresent(csiToken);
            if (cachedUserSession != null) {
                log.info("Cache hit for CSI token: {}", csiToken);
                exchange.getAttributes().put("grpcSession", cachedUserSession);
                return chain.filter(exchange);
            }

            return authServiceStub.getUserSessionPermissions(request)
                    .flatMap(grpcResponse -> {
                        if (grpcResponse.getCore().hasAccessRestriction()){
                            //terminate the request here
                            exchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
                        }

                        log.info("Received gRPC response for CSI token: {}", csiToken);

                        // Cache the full gRPC response
                       csiPermissionCache.put(csiToken, grpcResponse);

                        // Store in request context for downstream filters/handlers
                        exchange.getAttributes().put("grpcSession", grpcResponse);

                        return chain.filter(exchange);
                    })
                    .onErrorResume(e -> {
                        log.error("gRPC error in PermissionFilter", e);
                        exchange.getResponse().setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
                        return exchange.getResponse().setComplete();
                    });
        };
    }
}

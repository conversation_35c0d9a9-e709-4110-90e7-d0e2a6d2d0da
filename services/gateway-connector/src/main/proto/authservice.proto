syntax = "proto3";

package authservice;

option java_multiple_files = true;
option java_package = "authservice";
option java_outer_classname = "AuthSessionProto";

message CoreAuthHeader {
  string Authorization = 1;
  string x_csi = 2;
}



message UserSessionAccessRestriction {
  string name = 1;
  repeated string enforcement_exclusions = 2;
  repeated string ips = 3;
}


message Active_Session {
  string id = 1;
  string log_in_time = 2;
  string log_in_ip = 3;
}

message EffectiveGroups {
  string id = 1;
  string name = 2;
}

message Role {
  string id = 1;
  string name = 2;
}

message ScopeObject {
  string type = 1;
  string value = 2;
}

message RoleAndScope {
  Role role = 1;
  repeated ScopeObject scope = 2;
}

message CoreSessionPermissions {
  string user_id = 1;
  string user_uuid = 2;
  int32 org_id = 3;
  string pce_fqdn = 4;
  string org_uuid = 5;
  bool locked = 6;
  int32 session_timeout_minutes = 7;
  repeated RoleAndScope permissions = 8;
  bool permissions_changed = 9;
  string user_type = 10;
  UserSessionAccessRestriction access_restriction = 11;
}

message CloudsecurePermissions {
  string tenant_id = 1;
  string user_id = 2;
  repeated string permissions = 3;
}

message GetUserSessionPermissionsRequest {
  string csi = 1;
  string xff = 2;
  string referrer = 3;
}

message GetUserSessionPermissionsResponse {
  CoreSessionPermissions core = 1;
  CloudsecurePermissions cloudsecure = 2;
  bool permissions_changed = 3;
  string last_updated_at = 4;

}

// For insights query service
message CloudAuthHeader {
  string x_tenant_id = 1;
  string cookie = 2;
}

message SessionCoreProduct {
  string org_id = 1;
  string org_uuid = 2;
  CoreAuthHeader auth_header = 3;
}

message SessionCloudProduct {
  string tenant_id = 1;
  CloudAuthHeader auth_header = 2;
}
message SessionProducts {
  SessionCoreProduct core = 1;
  SessionCloudProduct cloudsecure = 2;
}

// gRPC Service
service AuthService {
  rpc GetUserSessionPermissions(GetUserSessionPermissionsRequest) returns (GetUserSessionPermissionsResponse) {}
}

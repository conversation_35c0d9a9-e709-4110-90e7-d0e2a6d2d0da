package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsRequest;
import authservice.GetUserSessionPermissionsResponse;
import authservice.ReactorAuthServiceGrpc;
import com.github.benmanes.caffeine.cache.Cache;
import com.illumio.data.filter.utils.DummyUserSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PermissionFilterTest {
    @Mock
    private Cache<String, GetUserSessionPermissionsResponse> cache;

    @Mock
    private ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub;

    @Mock
    private GatewayFilterChain chain;

    private PermissionFilter filter;

    @BeforeEach
    void setup() {
        filter = new PermissionFilter(cache, authServiceStub);
    }

    @Test
    void skipsIfGrpcSessionAlreadyPresent() {
        String csi = "csi";
        ServerWebExchange exchange = MockServerWebExchange.from(MockServerHttpRequest.get("/some-path")
                .header("X-csi", csi)
                .header("X-Forwarded-For", "127.0.0.1").build());
        exchange.getAttributes().put("grpcSession", GetUserSessionPermissionsResponse.getDefaultInstance());

        when(chain.filter(exchange)).thenReturn(Mono.empty());

        StepVerifier.create(filter.apply(new PermissionFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchange);
    }

    @Test
    void returnsUnauthorizedIfCsiMissing() {
        ServerWebExchange exchange = MockServerWebExchange.from(MockServerHttpRequest.get("/no-csi").build());

        StepVerifier.create(filter.apply(new PermissionFilter.Config()).filter(exchange, chain))
                .expectComplete()
                .verify();

        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
    }

    @Test
    void usesCachedResponseIfPresent() {
        String csi = "abc123";
        GetUserSessionPermissionsResponse cachedResponse = GetUserSessionPermissionsResponse.getDefaultInstance();
        when(cache.getIfPresent(csi)).thenReturn(cachedResponse);
        when(chain.filter(any())).thenReturn(Mono.empty());

        ServerWebExchange exchange = MockServerWebExchange.from(MockServerHttpRequest.get("/cached")
                .header("X-csi", csi)
                .header("X-Forwarded-For", "127.0.0.1")
                .build());

        StepVerifier.create(filter.apply(new PermissionFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        assertEquals(cachedResponse, exchange.getAttribute("grpcSession"));
    }

    @Test
    void grpcCallSucceeds_CachesAndSetsAttribute() {
        GetUserSessionPermissionsResponse grpcResponse = DummyUserSession.createDummySessionResponse();

        GetUserSessionPermissionsRequest request = DummyUserSession.createDummySessionRequest();
        String csi = request.getCsi();

        when(authServiceStub.getUserSessionPermissions(request)).thenReturn(Mono.just(grpcResponse));
        when(chain.filter(any())).thenReturn(Mono.empty());

        ServerWebExchange exchange = MockServerWebExchange.from(MockServerHttpRequest.get("/grpc")
                .header("X-csi", request.getCsi())
                .header("X-Forwarded-For", request.getXff())
                .header("Referer", request.getReferrer())
                .build());

        StepVerifier.create(filter.apply(new PermissionFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        assertEquals(grpcResponse, exchange.getAttribute("grpcSession"));
//        verify(cache).put(eq(csi), eq(grpcResponse));
        verify(chain).filter(exchange);
    }

    @Test
    void grpccallHas_AccessRestriction() {
        GetUserSessionPermissionsResponse grpcResponse = DummyUserSession.getDummyAccessRestrictionResponse();

        GetUserSessionPermissionsRequest request = DummyUserSession.createDummySessionRequest();
        String csi = request.getCsi();

        when(authServiceStub.getUserSessionPermissions(request)).thenReturn(Mono.just(grpcResponse));
        when(chain.filter(any())).thenReturn(Mono.empty());

        ServerWebExchange exchange = MockServerWebExchange.from(MockServerHttpRequest.get("/grpc")
                .header("X-csi", request.getCsi())
                .header("X-Forwarded-For", request.getXff())
                .header("Referer", request.getReferrer())
                .build());

        StepVerifier.create(filter.apply(new PermissionFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        assertEquals(HttpStatus.FORBIDDEN, exchange.getResponse().getStatusCode());
        //verify(cache).put(eq(csi), eq(grpcResponse));
        verify(chain).filter(exchange);

    }
}
package com.illumio.data.filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class AsgardFilterTest {
    private AsgardFilter filter;

    private static String SUBDOMIAN_HEADER = "X-tenant-subdomain";
    @BeforeEach
    void setUp() {
        filter = new AsgardFilter();
    }

    @Test
    void testApplyFilterLogsMessageAndContinuesChain() {
        // Setup config with a test message
        AsgardFilter.Config config = new AsgardFilter.Config();
        config.setMessage("Test Asgard Filter");

        // Create the GatewayFilter
        GatewayFilter gatewayFilter = filter.apply(config);

        // Mock the exchange and chain
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/asgard/test")
                .build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(exchange)).thenReturn(Mono.empty());

        // Apply filter and verify it completes
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        // Verify that the chain was called
        verify(chain, times(1)).filter(exchange);
    }

    @Test
    void testSubdomainExtractionFromRefererInHeaders() {
        // Setup config with a test message
        AsgardFilter.Config config = new AsgardFilter.Config();
        config.setMessage("Test Subdomain Extraction");

        // Create the GatewayFilter
        GatewayFilter gatewayFilter = filter.apply(config);

        // Mock the exchange and chain
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/asgard/test")
                .header("referer", "https://xyz.console.illumio.io/#login")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(exchange)).thenReturn(Mono.empty());

        // Apply filter and verify it completes
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        // Verify that the chain was called
        verify(chain, times(1)).filter(exchange);
        assertEquals("xyz", exchange.getRequest().getHeaders().getFirst(SUBDOMIAN_HEADER));
    }
}
package com.illumio.data.filter.utils;

import authservice.*;

public class DummyUserSession {

    public static GetUserSessionPermissionsResponse createDummySessionResponse() {
        return GetUserSessionPermissionsResponse.newBuilder()
                .setPermissionsChanged(true)
                .setLastUpdatedAt("Thu May 01 2025 15:54:46 GMT+0000 (Coordinated Universal Time)")
                .setCore(CoreSessionPermissions.newBuilder()
                        .setUserId("user123")
                        .setUserUuid("uuid-abc-123")
                        .setOrgId(101)
                        .setPceFqdn("example.com:443")
                        .setOrgUuid("org-xyz-456")
                        .setLocked(false)
                        .setSessionTimeoutMinutes(30)
                        .setUserType("admin")
                        .addPermissions(RoleAndScope.newBuilder()
                                .setRole(Role.newBuilder()
                                        .setId("r1")
                                        .setName("SuperAdmin")
                                        .build())
                                .addScope(ScopeObject.newBuilder()
                                        .setType("app")
                                        .setValue("infra")
                                        .build())
                                .addScope(ScopeObject.newBuilder()
                                        .setType("env")
                                        .setValue("beta")
                                        .build())
                                .build())
                        .build())
                .setCloudsecure(CloudsecurePermissions.newBuilder()
                        .addPermissions("search:w")
                        .addPermissions("service_account:w")
                        .addPermissions("connectors:w")
                        .addPermissions("preferences:w")
                        .addPermissions("app_discovery_rules:w")
                        .addPermissions("events:w")
                        .addPermissions("onboarding:w")
                        .addPermissions("tenant:w")
                        .addPermissions("traffic:w")
                        .addPermissions("user:w")
                        .addPermissions("insights:r")
                        .addPermissions("app_approval:w")
                        .addPermissions("cloud_map:w")
                        .addPermissions("dashboard:w")
                        .addPermissions("tag_to_label:w")
                        .addPermissions("roles:r")
                        .addPermissions("reporting:w")
                        .addPermissions("deployments:w")
                        .addPermissions("org_policy:w")
                        .addPermissions("usage:w")
                        .addPermissions("k8s_cluster_management:w")
                        .addPermissions("billing_admin:w")
                        .addPermissions("app_policy:w")
                        .addPermissions("inventory:w")
                        .setTenantId("tenant-123")
                        .setUserId("user-123")
                        .build())
                .build();
    }

    public static GetUserSessionPermissionsResponse getDummyAccessRestrictionResponse() {
        return GetUserSessionPermissionsResponse.newBuilder()
                .setPermissionsChanged(true)
                .setLastUpdatedAt("Thu May 01 2025 15:54:46 GMT+0000 (Coordinated Universal Time)")
                .setCore(CoreSessionPermissions.newBuilder()
                        .setUserId("user123")
                        .setUserUuid("uuid-abc-123")
                        .setOrgId(101)
                        .setPceFqdn("example.com:443")
                        .setOrgUuid("org-xyz-456")
                        .setLocked(false)
                        .setAccessRestriction(
                                UserSessionAccessRestriction.newBuilder()
                                        .addEnforcementExclusions("API")
                                        .build()
                        )
                        .setSessionTimeoutMinutes(30)
                        .setUserType("admin")
                        .addPermissions(RoleAndScope.newBuilder()
                                .setRole(Role.newBuilder()
                                        .setId("r1")
                                        .setName("SuperAdmin")
                                        .build())
                                .addScope(ScopeObject.newBuilder()
                                        .setType("app")
                                        .setValue("infra")
                                        .build())
                                .addScope(ScopeObject.newBuilder()
                                        .setType("env")
                                        .setValue("beta")
                                        .build())
                                .build())
                        .build())
                .setCloudsecure(CloudsecurePermissions.newBuilder()
                        .addPermissions("search:w")
                        .addPermissions("service_account:w")
                        .addPermissions("connectors:w")
                        .addPermissions("preferences:w")
                        .addPermissions("app_discovery_rules:w")
                        .addPermissions("events:w")
                        .addPermissions("onboarding:w")
                        .addPermissions("tenant:w")
                        .addPermissions("traffic:w")
                        .addPermissions("user:w")
                        .addPermissions("insights:r")
                        .addPermissions("app_approval:w")
                        .addPermissions("cloud_map:w")
                        .addPermissions("dashboard:w")
                        .addPermissions("tag_to_label:w")
                        .addPermissions("roles:r")
                        .addPermissions("reporting:w")
                        .addPermissions("deployments:w")
                        .addPermissions("org_policy:w")
                        .addPermissions("usage:w")
                        .addPermissions("k8s_cluster_management:w")
                        .addPermissions("billing_admin:w")
                        .addPermissions("app_policy:w")
                        .addPermissions("inventory:w")
                        .setTenantId("tenant-123")
                        .setUserId("user-123")
                        .build())
                .build();
    }

    public static GetUserSessionPermissionsRequest createDummySessionRequest() {
        return GetUserSessionPermissionsRequest
                .newBuilder()
                .setReferrer("PermissionFilter")
                .setCsi("csi")
                .setXff("127.0.0.1")
                .build();
    }
}

package com.illumio.data.filter;

import authservice.*;
import com.illumio.data.filter.utils.DummyUserSession;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Map;

import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CoreFilterTest {

    @Mock
    private JwtService jwtService;

    @Mock
    private MeterRegistry meterRegistry;

    @Mock
    private Counter largePermissionsCounter;

    private CoreFilter coreFilter;

    @BeforeEach
    void setUp() throws Exception {
        when(meterRegistry.counter("gateway.large_permissions_counter")).thenReturn(largePermissionsCounter);
        coreFilter = new CoreFilter(jwtService, meterRegistry);
    }

    @Test
    void testFilter_RewritesUriAndAddsAuthorizationHeader() throws Exception {
        // --- Setup mock grpcResponse ---
        GetUserSessionPermissionsResponse grpcResponse = DummyUserSession.createDummySessionResponse();

        // Simulate JWT creation
        when(jwtService.generatePermissionsJwt(Mockito.anyMap(), Mockito.anyInt()))
                .thenReturn("dummy-jwt");

        // --- Setup mock exchange ---
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/core/test")
                .header("X-CSI", "test-csi-token")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("grpcSession", grpcResponse);

        // --- Setup mock GatewayFilterChain ---
        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class)))
                .thenAnswer(invocation -> {
                    ServerWebExchange mutatedExchange = invocation.getArgument(0);
                    URI mutatedUri = mutatedExchange.getRequest().getURI();
                    String gateway_header = mutatedExchange.getRequest().getHeaders().getFirst(GATEWAY_TOKEN);

                    assertEquals("https", mutatedUri.getScheme());
                    assertEquals("example.com", mutatedUri.getHost());
                    assertEquals(443, mutatedUri.getPort());  // Parsed from "example.com:443
                    assertNotNull(gateway_header);
                    return Mono.empty();
                });

        // --- Run the filter ---
        coreFilter.apply(new CoreFilter.Config()).filter(exchange, chain).block();
    }

    @Test
    void testFilter_HandlesLargeGrpcResponse() throws Exception {
        CoreSessionPermissions.Builder coreBuilder = CoreSessionPermissions.newBuilder()
                .setUserId("user123")
                .setUserUuid("uuid-abc-123")
                .setOrgId(101)
                .setPceFqdn("example.com:443")
                .setOrgUuid("org-xyz-456")
                .setLocked(false)
                .setSessionTimeoutMinutes(30)
                .setUserType("admin");

        for (int i = 0; i < 1000; i++) {
            RoleAndScope permission = RoleAndScope.newBuilder()
                    .setRole(Role.newBuilder().setId("r" + i).setName("role" + i))
                    .addScope(ScopeObject.newBuilder().setType("env").setValue("prod"))
                    .build();
            coreBuilder.addPermissions(permission);
        }

        GetUserSessionPermissionsResponse grpcResponse = GetUserSessionPermissionsResponse.newBuilder()
                .setPermissionsChanged(true)
                .setLastUpdatedAt("Thu May 01 2025 15:54:46 GMT+0000 (Coordinated Universal Time)")
                .setCore(coreBuilder.build())
                .build();

        ArgumentCaptor<Map<String, Object>> captor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<Integer> timeoutCaptor = ArgumentCaptor.forClass(Integer.class);
        Mockito.when(jwtService.generatePermissionsJwt(captor.capture(), timeoutCaptor.capture()))
                .thenReturn("large-jwt");

        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/core/test")
                .header("X-CSI", "oversized-token")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("grpcSession", grpcResponse);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class)))
                .thenAnswer(invocation -> {
                    ServerWebExchange mutatedExchange = invocation.getArgument(0);
                    String gateway_header = mutatedExchange.getRequest().getHeaders().getFirst(GATEWAY_TOKEN);
                    assertNotNull(gateway_header);
                    return Mono.empty();
                });
        coreFilter.apply(new CoreFilter.Config()).filter(exchange, chain).block();
        verify(largePermissionsCounter, times(1)).increment();
        Map<String, Object> capturedClaims = captor.getValue();
        Map<String, Object> core = (Map<String, Object>) capturedClaims.get("core");
        assertEquals(true, core.get("effective_groups_excluded"));
    }

    @Test
    void testConfigBeanAccessors() {
        CoreFilter.Config config = new CoreFilter.Config();
        config.setMessage("test");
        assertEquals("test", config.getMessage());
    }

}

package com.illumio.data.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.pojo.FlowKey;
import com.illumio.data.pojo.FlowValue;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.common.serialization.Serde;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class SerdesConfig {
    private final ObjectMapper objectMapper;

    @Bean
    public Serde<IllumioCommonSecurityFlow> illumioCommonSecurityFlowSerde() {
        return new CustomJsonSerde<>(objectMapper, IllumioCommonSecurityFlow.class);
    }

    @Bean
    public Serde<FlowValue> flowSerde() {
        return new CustomJsonSerde<>(objectMapper, FlowValue.class);
    }

    @Bean
    public Serde<FlowKey> flowKeySerde() {
        return new CustomJsonSerde<>(objectMapper, FlowKey.class);
    }
}

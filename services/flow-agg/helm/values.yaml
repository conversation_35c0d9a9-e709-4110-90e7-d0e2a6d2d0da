# Default values for flow-agg.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"

image:
#  repositoryBase: ************.dkr.ecr.us-west-2.amazonaws.com/debug/
  repositoryBase: illum.azurecr.io/
#  repositoryName: iz-kstream-example
  repositoryName: flow-agg
  # Overrides the image tag whose default is the chart appVersion.
  tag: # value given at helm deployment
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

ports:
  - name: admin
    port: 8084
  - name: rest
    port: 8081

servicePorts:
  - name: rest
    podPort: rest
    servicePort: 8081

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

resources:
  limits:
    cpu: 100m
    memory: 4Gi
  requests:
    cpu: 100m
    memory: 4Gi

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

nodeSelector: {}

tolerations: []

affinity: {}

csEnv: dev

otel:
  collector: 'cs-jaeger-collector.default.svc.cluster.local:4318'

probes:
  liveness:
    enabled: false
    path: "/health"
    threshold: 30
    periodSec: 10
  startup:
    enabled: false
    path: "/health"
    threshold: 100
    periodSec: 5

configMapData:
  logging:
    level:
      ROOT: INFO
      org:
        apache:
          kafka: INFO
  spring:
    application:
      name: flow-kstreams
    output:
      ansi:
        enabled: ALWAYS
  #  main:
  #    web-application-type: none
  server:
    port : 8089

  flow-agg-config:
    kstreams-config:
      application-id: flow-agg
      bootstrap-servers: test-arch-eventhub.servicebus.windows.net:9093
      state-store-cache-max-bytes: 10485760 # 10 MB across all stream threads
      commit-interval-ms: 1000 # 1s
      state-store-dir: /rocksdb-dir
      #    event hub config
      is-sasl: true

    streams-builder-config:
      input-topic: raw-flow-v1
      window-size: 60s
      state-store-name: flow-state-store
      state-store-retention: 2h
      punctuate-interval: 10s
      output-topic: aggregated-flow-v1

eventhub:
  password: # should give at deployment time

# Kafka Streams RocksDB state store
diskSize: 10Gi
storageClass: default # should give at deployment time

extraLabels: {}

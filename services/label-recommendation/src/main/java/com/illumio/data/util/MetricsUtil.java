package com.illumio.data.util;

import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;
import org.springframework.stereotype.Component;

@Component
public class MetricsUtil {
    private final LongCounter recordsWithErrors;
    private final LongCounter recordsEnriched;
    private final LongCounter recordsUnenrichedMissingLabels;

    public static final String METRIC_RECORDS_WITH_ERRORS = "records_error";
    public static final String METRIC_RECORDS_ENRICHED = "records_enriched";
    public static final String METRIC_RECORDS_UNENRICHED_MISSING_LABELS = "records_unenriched_missing_labels";

    public MetricsUtil(Meter meter) {
        this.recordsWithErrors = meter
                .counterBuilder(METRIC_RECORDS_WITH_ERRORS)
                .setDescription("No of flow records processed with errors")
                .build();
        this.recordsEnriched = meter
                .counterBuilder(METRIC_RECORDS_ENRICHED)
                .setDescription("No of flow records processed with enrichment")
                .build();
        this.recordsUnenrichedMissingLabels = meter
                .counterBuilder(METRIC_RECORDS_UNENRICHED_MISSING_LABELS)
                .setDescription("No of flow records processed without enrichment due to missing labels")
                .build();
    }

    public void incrementRecordsWithErrors() {
        recordsWithErrors.add(1L);
    }

    public void incrementRecordsEnriched() {
        recordsEnriched.add(1L);
    }

    public void incrementRecordsUnenrichedMissingLabels() {
        recordsUnenrichedMissingLabels.add(1L);
    }
}

package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.illumio.data.configuration.ResilienceConfig;
import com.illumio.data.metrics.DecoratorMetrics;
import inventory.Cache;
import inventory.ReactorInventoryCacheServiceGrpc;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.reactor.circuitbreaker.operator.CircuitBreakerOperator;
import io.github.resilience4j.reactor.retry.RetryOperator;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class InventoryResourceMetaDecorator {
    private static final String ILLUMIO_TENANT_ID = "IllumioTenantId";
    private static final String CS_SRC_ID = "CSSrcId";
    private static final String CS_DEST_ID = "CSDestId";

    private final ReactorInventoryCacheServiceGrpc.ReactorInventoryCacheServiceStub inventory;
    private final ObjectMapper objectMapper;
    private final DecoratorMetrics metrics;

    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final RetryRegistry retryRegistry;
    private final ResilienceConfig resilienceConfig;

    private CircuitBreaker circuitBreaker;
    private Retry retry;

    @PostConstruct
    public void init() {
        this.circuitBreaker = circuitBreakerRegistry.circuitBreaker("inventory-meta-decorator");
        this.retry = retryRegistry.retry("inventory-meta-decorator");

        // Add event listeners
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> log.info("Circuit Breaker '{}' state changed from {} to {}",
                        event.getCircuitBreakerName(),
                        event.getStateTransition().getFromState(),
                        event.getStateTransition().getToState()))
                .onError(event -> log.warn("Circuit Breaker '{}' recorded error: {}",
                        event.getCircuitBreakerName(),
                        event.getThrowable().getMessage()))
                .onSuccess(event -> log.debug("Circuit Breaker '{}' recorded success",
                        event.getCircuitBreakerName()));

        retry.getEventPublisher()
                .onRetry(event -> log.info("Retry attempt {} of {}: {}",
                        event.getNumberOfRetryAttempts(),
                        retry.getRetryConfig().getMaxAttempts(),
                        event.getLastThrowable().getMessage()));
    }

    public Mono<JsonNode> decorate(JsonNode jsonNode) {
        return Mono.just(jsonNode)
                .filter(this::valid)
                .switchIfEmpty(Mono.just(jsonNode))
                .map(
                        j -> {
                            var request = Cache.GetCachedResourcesRequest.newBuilder();
                            request.setTenantId(jsonNode.get(ILLUMIO_TENANT_ID).asText());
                            if (jsonNode.hasNonNull(CS_SRC_ID)) {
                                var id = jsonNode.get(CS_SRC_ID);
                                if (id.isTextual()) {
                                    request.addIds(id.asText());
                                }
                            }
                            if (jsonNode.hasNonNull(CS_DEST_ID)) {
                                var id = jsonNode.get(CS_DEST_ID);
                                if (id.isTextual()) {
                                    request.addIds(id.asText());
                                }
                            }
                            return request.build();
                        })
                .doOnNext(
                        getCachedResourcesRequest ->
                                log.debug(
                                        "Sending getCachedResources request {}",
                                        getCachedResourcesRequest))
                .flatMap(request -> {
                    long startTime = System.currentTimeMillis();
                    return Mono.defer(() -> inventory.getCachedResources(request))
                            .timeout(resilienceConfig.getTimeoutDuration())
                            .transform(RetryOperator.of(retry))
                            .transform(CircuitBreakerOperator.of(circuitBreaker))
                            .doFinally(signalType -> {
                                long latency = System.currentTimeMillis() - startTime;
                                metrics.getResourceMetaLatency().record(latency);
                                log.debug("Resource Meta gRPC call took {}ms", latency);
                            });
                })
                .doOnNext(
                        getCachedResourcesResponse ->
                                log.debug("Received response: {}", getCachedResourcesResponse))
                .share()
                .publishOn(Schedulers.boundedElastic())
                .map(
                        getCachedResourcesResponse ->
                                decorateWithResponse(jsonNode, getCachedResourcesResponse));
    }

    private JsonNode decorateWithResponse(
            JsonNode jsonNode, Cache.GetCachedResourcesResponse getCachedResourcesResponse) {
        var objectNode = (ObjectNode) jsonNode;
        for (var cachedResource : getCachedResourcesResponse.getResourcesList()) {
            if (cachedResource.getItemCase().equals(Cache.CachedResource.ItemCase.CLOUD_RESOURCE)) {
                var cloudResource = cachedResource.getCloudResource();
                var directions = direction(jsonNode, cloudResource.getId());
                for (var direction : directions) {
                    if (cloudResource.getNetworkCspIdsCount() > 0) {
                        objectNode.put(direction + "VnetId", cloudResource.getNetworkCspIds(0));
                    }
                    // sub
                    objectNode.put(direction + "SubId", cloudResource.getAccountId());
                    // res
                    objectNode.put(direction + "ResId", cloudResource.getCspId());
                    // region
                    objectNode.put(direction + "Region", cloudResource.getRegion());
                    // cloudprovider
                    objectNode.put(direction + "CloudProvider", cloudResource.getCloud());
                    // tenant
                    objectNode.put(direction + "TenantId", cloudResource.getTenantId());
                    // tags
                    if (cloudResource.getTagsCount() > 0) {
                        try {
                            var tagsString =
                                    objectMapper.writeValueAsString(cloudResource.getTagsMap());
                            objectNode.put(direction + "CloudTags", tagsString);
                        } catch (JsonProcessingException e) {
                            log.warn(
                                    "Could not convert tags to json string for tags {} flow {}",
                                    cloudResource.getTagsMap(),
                                    jsonNode);
                        }
                    }
                }
            } else if (cachedResource
                    .getItemCase()
                    .equals(Cache.CachedResource.ItemCase.EXTERNAL_RESOURCE)) {
                var externalResource = cachedResource.getExternalResource();
                var directions = direction(jsonNode, externalResource.getId());
                for (var direction : directions) {
                    // lawtenant
                    objectNode.put("LawTenantId", externalResource.getExternalTenantId());
                    // user
                    if (externalResource.getType().equals("user")) {
                        objectNode.put(direction + "UserId", externalResource.getEntityId());
                    }
                    // device
                    if (externalResource.getType().equals("device")) {
                        objectNode.put(direction + "DeviceId", externalResource.getEntityId());
                    }
                    // firewall
                    if (externalResource.getType().equals("firewall")) {
                        objectNode.put(direction + "FirewallId", externalResource.getEntityId());
                    }
                    // tenant
                    objectNode.put(direction + "TenantId", externalResource.getTenantId());
                }
            } else {
                log.warn(
                        "Unsupported resource type {} for flow {}",
                        cachedResource.getItemCase(),
                        jsonNode);
            }
        }
        return objectNode;
    }

    private List<String> direction(JsonNode jsonNode, String id) {
        var directions = new ArrayList<String>();
        if (jsonNode.hasNonNull(CS_SRC_ID)
                && jsonNode.get(CS_SRC_ID).isTextual()
                && id.equals(jsonNode.get(CS_SRC_ID).asText())) {
            directions.add("Src");
        }
        if (jsonNode.hasNonNull(CS_DEST_ID)
                && jsonNode.get(CS_DEST_ID).isTextual()
                && id.equals(jsonNode.get(CS_DEST_ID).asText())) {
            directions.add("Dest");
        }
        if (directions.isEmpty()) {
            log.warn(
                    "Unexpected ID from getCachedResources {}, ignoring for flow {}", id, jsonNode);
        }
        return directions;
    }

    private boolean valid(JsonNode jsonNode) {
        if (!jsonNode.isObject()) {
            log.warn("JsonNode is not an ObjectNode, skipping decoration for {}", jsonNode);
            return false;
        }
        if (!hasTenantId(jsonNode)) {
            log.warn(
                    "{} is required, skipping decoration for flow {}", ILLUMIO_TENANT_ID, jsonNode);
            return false;
        }
        if (!hasOneId(jsonNode)) {
            log.warn(
                    "One of {} or {} is required, skipping flow {}",
                    CS_SRC_ID,
                    CS_DEST_ID,
                    jsonNode);
            return false;
        }
        return true;
    }

    private boolean hasOneId(JsonNode jsonNode) {
        return (jsonNode.hasNonNull(CS_SRC_ID) && jsonNode.get(CS_SRC_ID).isTextual())
                || (jsonNode.hasNonNull(CS_DEST_ID) && jsonNode.get(CS_DEST_ID).isTextual());
    }

    boolean hasTenantId(JsonNode jsonNode) {
        return jsonNode.hasNonNull(ILLUMIO_TENANT_ID)
                && jsonNode.get(ILLUMIO_TENANT_ID).isTextual();
    }
}

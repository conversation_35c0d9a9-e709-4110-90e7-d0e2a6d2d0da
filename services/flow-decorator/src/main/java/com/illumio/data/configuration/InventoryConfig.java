package com.illumio.data.configuration;

import inventory.ReactorInventoryCacheServiceGrpc;

import io.grpc.Grpc;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import io.grpc.netty.shaded.io.netty.handler.ssl.util.InsecureTrustManagerFactory;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "inventory-config")
@Data
public class InventoryConfig {
    private String host;
    private Integer port;

    @Bean
    public ReactorInventoryCacheServiceGrpc.ReactorInventoryCacheServiceStub stub() {
        var creds =
                TlsChannelCredentials.newBuilder()
                        .trustManager(InsecureTrustManagerFactory.INSTANCE.getTrustManagers())
                        .build();
        ManagedChannel managedChannel =
                Grpc.newChannelBuilderForAddress(host, port, creds)
                        .build();
        return ReactorInventoryCacheServiceGrpc.newReactorStub(managedChannel);
    }
}

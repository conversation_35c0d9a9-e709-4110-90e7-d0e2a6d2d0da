package com.illumio.data;

import inventory.Cache;
import inventory.InventoryCacheServiceGrpc;
import inventory.ReactorInventoryCacheServiceGrpc;
import io.grpc.Grpc;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import io.grpc.netty.shaded.io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@Slf4j
@SpringBootApplication
public class FlowDecoratorApplication {
    public static void main(String[] args) {
        SpringApplication.run(FlowDecoratorApplication.class, args);
    }
}

package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.illumio.data.configuration.ResilienceConfig;
import com.illumio.data.metrics.DecoratorMetrics;
import inventory.Cache;
import inventory.ReactorInventoryCacheServiceGrpc;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.reactor.circuitbreaker.operator.CircuitBreakerOperator;
import io.github.resilience4j.reactor.retry.RetryOperator;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;

@Component
@RequiredArgsConstructor
@Slf4j
public class InventoryResourceIdDecorator {
    private static final String ILLUMIO_TENANT_ID = "IllumioTenantId";
    private static final String SRC_IP = "SrcIP";
    private static final String DEST_IP = "DestIP";
    private static final String SOURCE_MAC_ADDRESS = "SourceMACAddress";
    private static final String DESTINATION_MAC_ADDRESS = "DestinationMACAddress";
    private static final String CS_SRC_ID = "CSSrcId";
    private static final String CS_DEST_ID = "CSDestId";

    private final ReactorInventoryCacheServiceGrpc.ReactorInventoryCacheServiceStub inventory;
    private final DecoratorMetrics metrics;
    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final RetryRegistry retryRegistry;
    private final ResilienceConfig resilienceConfig;

    private CircuitBreaker circuitBreaker;
    private Retry retry;

    @PostConstruct
    public void init() {
        this.circuitBreaker = circuitBreakerRegistry.circuitBreaker("inventory-id-decorator");
        this.retry = retryRegistry.retry("inventory-id-decorator");

        // Add event listeners
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> log.info("Circuit Breaker '{}' state changed from {} to {}",
                        event.getCircuitBreakerName(),
                        event.getStateTransition().getFromState(),
                        event.getStateTransition().getToState()))
                .onError(event -> log.warn("Circuit Breaker '{}' recorded error: {}",
                        event.getCircuitBreakerName(),
                        event.getThrowable().getMessage()))
                .onSuccess(event -> log.debug("Circuit Breaker '{}' recorded success",
                        event.getCircuitBreakerName()));

        retry.getEventPublisher()
                .onRetry(event -> log.info("Retry attempt {} of {}: {}",
                        event.getNumberOfRetryAttempts(),
                        retry.getRetryConfig().getMaxAttempts(),
                        event.getLastThrowable().getMessage()));
    }

    public Mono<JsonNode> decorate(JsonNode jsonNode) {
        return Mono.just(jsonNode)
                .filter(this::valid)
                .switchIfEmpty(Mono.just(jsonNode))
                .map(
                        __ ->
                                new RequestParameters(
                                        jsonNode.get(ILLUMIO_TENANT_ID).asText(),
                                        jsonNode.hasNonNull(SRC_IP)
                                                ? jsonNode.get(SRC_IP).asText()
                                                : null,
                                        jsonNode.hasNonNull(DEST_IP)
                                                ? jsonNode.get(DEST_IP).asText()
                                                : null,
                                        jsonNode.hasNonNull(SOURCE_MAC_ADDRESS)
                                                ? jsonNode.get(SOURCE_MAC_ADDRESS).asText()
                                                : null,
                                        jsonNode.hasNonNull(DESTINATION_MAC_ADDRESS)
                                                ? jsonNode.get(DESTINATION_MAC_ADDRESS).asText()
                                                : null))
                .flatMap(requestParameters -> {
                    long startTime = System.currentTimeMillis();
                    return lookupIps(jsonNode, requestParameters)
                            .timeout(resilienceConfig.getTimeoutDuration())
                            .transform(RetryOperator.of(retry))
                            .transform(CircuitBreakerOperator.of(circuitBreaker))
                            .doFinally(signalType -> {
                                long latency = System.currentTimeMillis() - startTime;
                                metrics.getResourceIdLatency().record(latency);
                                log.debug("Resource ID gRPC call took {}ms", latency);
                            });
                });
    }

    Mono<JsonNode> lookupIps(JsonNode jsonNode, RequestParameters requestParameters) {
        return Mono.just(requestParameters)
                .map(
                        __ -> {
                            var request = Cache.LookupRequest.newBuilder();
                            if (requestParameters.hasSrc()) {
                                var macIP = Cache.MacIP.newBuilder();
                                if (null != requestParameters.srcIP()) {
                                    macIP.setIp(requestParameters.srcIP());
                                }
                                if (null != requestParameters.srcMAC()) {
                                    macIP.setMac(requestParameters.srcMAC());
                                }
                                request.addMacIp(macIP);
                            }
                            if (requestParameters.hasDest()) {
                                var macIP = Cache.MacIP.newBuilder();
                                if (null != requestParameters.destIP()) {
                                    macIP.setIp(requestParameters.destIP());
                                }
                                if (null != requestParameters.destMAC()) {
                                    macIP.setMac(requestParameters.destMAC());
                                }
                                request.addMacIp(macIP);
                            }
                            var req = request.setTenantId(requestParameters.tenantId()).build();
                            return req;
                        })
                .doOnNext(lookupRequest -> log.debug("Sending lookup request {}", lookupRequest))
                .flatMap(lookupRequest -> Mono.defer(() -> inventory.lookup(lookupRequest)))
                .publishOn(Schedulers.boundedElastic())
                .map(
                        lookupResponse ->
                                decorateWithResponse(jsonNode, requestParameters, lookupResponse));
    }

    boolean valid(JsonNode jsonNode) {
        if (!jsonNode.isObject()) {
            log.warn("JsonNode is not an ObjectNode, skipping decoration for {}", jsonNode);
        }
        if (!hasTenantId(jsonNode)) {
            log.warn(
                    "{} is required, skipping decoration for flow {}", ILLUMIO_TENANT_ID, jsonNode);
            return false;
        }
        if (!hasSrcOrDest(jsonNode)) {
            log.warn(
                    "One of Source or Dest is required, skipping decoration for flow {}", jsonNode);
            return false;
        }
        return true;
    }

    boolean hasTenantId(JsonNode jsonNode) {
        return jsonNode.hasNonNull(ILLUMIO_TENANT_ID)
                && jsonNode.get(ILLUMIO_TENANT_ID).isTextual();
    }

    boolean hasSrcOrDest(JsonNode jsonNode) {
        return (jsonNode.hasNonNull(SRC_IP) && jsonNode.get(SRC_IP).isTextual())
                || (jsonNode.hasNonNull(DEST_IP) && jsonNode.get(DEST_IP).isTextual())
                || (jsonNode.hasNonNull(SOURCE_MAC_ADDRESS)
                        && jsonNode.get(SOURCE_MAC_ADDRESS).isTextual())
                || (jsonNode.hasNonNull(DESTINATION_MAC_ADDRESS)
                        && jsonNode.get(DESTINATION_MAC_ADDRESS).isTextual());
    }

    JsonNode decorateWithResponse(
            JsonNode jsonNode,
            RequestParameters requestParameters,
            Cache.LookupResponse lookupResponse) {
        log.debug("Starting decoration with response. Input: {}, Parameters: {}", jsonNode, requestParameters);
        log.debug("Lookup response: {}", lookupResponse);
        log.debug("Response list size: {}", lookupResponse.getRspList().size());

        for (var macIPRsp : lookupResponse.getRspList()) {
            log.debug("Processing response item: {}", macIPRsp);
            if (macIPRsp.isInitialized() && macIPRsp.hasMacIp()) {
                log.debug("MacIP: {}, Source IP: {}, Source MAC: {}",
                        macIPRsp.getMacIp(), requestParameters.srcIP(), requestParameters.srcMAC());

                if (macIPRsp.getMacIp().getIp().equals(requestParameters.srcIP())
                        || macIPRsp.getMacIp().getMac().equals(requestParameters.srcMAC())) {
                    if (macIPRsp.getIdCount() > 0) {
                        String srcId = macIPRsp.getId(0).getId();
                        ((ObjectNode) jsonNode).put(CS_SRC_ID, srcId);
                        log.debug("Added source ID: {} = {}", CS_SRC_ID, srcId);
                    } else {
                        log.debug("No IDs found for source match");
                    }
                } else if (macIPRsp.getMacIp().getIp().equals(requestParameters.destIP())
                        || macIPRsp.getMacIp().getMac().equals(requestParameters.destMAC())) {
                    if (macIPRsp.getIdCount() > 0) {
                        String destId = macIPRsp.getId(0).getId();
                        ((ObjectNode) jsonNode).put(CS_DEST_ID, destId);
                        log.debug("Added destination ID: {} = {}", CS_DEST_ID, destId);
                    } else {
                        log.debug("No IDs found for destination match");
                    }
                } else {
                    log.debug("No IP/MAC matches found");
                }
            } else {
                log.debug("Response item not initialized or missing MacIP");
            }
        }

        log.debug("Decoration complete, final result: {}", jsonNode);
        return jsonNode;
    }

    private record RequestParameters(
            String tenantId, String srcIP, String destIP, String srcMAC, String destMAC) {
        boolean hasSrc() {
            return srcIP != null || srcMAC != null;
        }

        boolean hasDest() {
            return destIP != null || destMAC != null;
        }
    }
}

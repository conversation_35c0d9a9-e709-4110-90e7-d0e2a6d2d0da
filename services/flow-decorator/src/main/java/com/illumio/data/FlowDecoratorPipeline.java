package com.illumio.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.InventoryResourceIdDecorator;
import com.illumio.data.components.InventoryResourceMetaDecorator;
import com.illumio.data.configuration.KafkaSenderConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.reactivestreams.Publisher;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;

@Slf4j
@Component
@RequiredArgsConstructor
public class FlowDecoratorPipeline {
    private final KafkaReceiver<String, String> kafkaReceiver;
    private final KafkaSender<String, String> kafkaSender;
    private final KafkaSenderConfig kafkaSenderConfig;
    private final ObjectMapper objectMapper;
    private final InventoryResourceIdDecorator inventoryResourceIdDecorator;
    private final InventoryResourceMetaDecorator inventoryResourceMetaDecorator;
    private Disposable disposable;

    void start() {
        this.disposable = startInternal().subscribe();
    }

    private Flux<SenderResult<String>> startInternal() {
        return kafkaSender
                .send(
                        kafkaReceiver
                                .receiveAutoAck()
                                .publishOn(Schedulers.boundedElastic())
                                .concatMap(r -> r)
                                .doOnNext(
                                        consumerRecord ->
                                                log.debug("Processing record {}", consumerRecord))
                                .flatMap(consumerRecord -> processConsumerRecord(consumerRecord)))
                .doOnNext(
                        stringSenderResult ->
                                log.debug(
                                        "Produced message for consumed data {} to {}",
                                        stringSenderResult.correlationMetadata(),
                                        stringSenderResult.recordMetadata()));
    }

    Publisher<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.fromCallable(() -> objectMapper.readTree(consumerRecord.value()))
                .flatMap(
                        originalFlow ->
                                inventoryResourceIdDecorator
                                        .decorate(originalFlow)
                                        .doOnNext(
                                                jsonNode ->
                                                        log.debug(
                                                                "Decorated with Resource ID flow {}",
                                                                jsonNode))
                                        .doOnError(
                                                throwable ->
                                                        log.error(
                                                                "Resource ID Decoration failed for flow {}, {}",
                                                                consumerRecord,
                                                                originalFlow))
                                        .onErrorReturn(originalFlow)
                                        .flatMap(
                                                idDecoratedFlow ->
                                                        inventoryResourceMetaDecorator
                                                                .decorate(idDecoratedFlow)
                                                                .doOnNext(
                                                                        metaDecoratedFlow ->
                                                                                log.debug(
                                                                                        "Decorated with Resource Meta flow {}",
                                                                                        metaDecoratedFlow))
                                                                .doOnError(
                                                                        throwable ->
                                                                                log.error(
                                                                                        "Resource Meta Decoration failed for flow {}, {}",
                                                                                        consumerRecord,
                                                                                        idDecoratedFlow))
                                                                .onErrorReturn(idDecoratedFlow)))
                .flatMap(
                        jsonNode ->
                                Mono.fromCallable(() -> objectMapper.writeValueAsString(jsonNode)))
                .doOnError(
                        throwable ->
                                log.error(
                                        "Processing failed for record {}",
                                        consumerRecord,
                                        throwable))
                .onErrorReturn(consumerRecord.value())
                .flatMap(
                        jsonNodeString ->
                                Mono.just(
                                        SenderRecord.create(
                                                new ProducerRecord<>(
                                                        kafkaSenderConfig.getTopic(),
                                                        consumerRecord.key(),
                                                        jsonNodeString),
                                                consumerRecord.toString())))
                .doOnNext(senderRecord -> log.debug("Sending record {}", senderRecord));
    }

    void stop() {
        disposable.dispose();
    }
}

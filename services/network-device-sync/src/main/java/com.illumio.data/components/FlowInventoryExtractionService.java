package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class FlowInventoryExtractionService {

    private final ObjectMapper objectMapper;

    public FlowInventory extractInventoryFromFlow(final FlowValue flowValue) {
        final Pair<Optional<InventoryItem>, Optional<InventoryItem>> srcDstDevices = extractDevicesFromFlow(flowValue);
        final Pair<Optional<InventoryItem>, Optional<InventoryItem>> srcDstIdentities = extractIdentitiesFromFlow(flowValue);
        final Optional<InventoryItem> firewall = extractFirewallFromFlow(flowValue);

        final List<InventoryItem> inventoryItems = new ArrayList<>();
        final List<DeviceToDeviceRelationship> deviceToDeviceRelationships = new ArrayList<>();
        final List<IdentityToDeviceRelationship> identityToDeviceRelationships = new ArrayList<>();
        srcDstDevices.getLeft().ifPresent(srcDevice -> {
            inventoryItems.add(srcDevice);
            srcDstDevices.getRight().ifPresent(destDevice -> deviceToDeviceRelationships.add(
                    DeviceToDeviceRelationship
                            .builder()
                            .srcDeviceId(srcDevice.getId())
                            .destDeviceId(destDevice.getId())
                            .build()));
            firewall.ifPresent(firewallDevice -> deviceToDeviceRelationships.add(
                    DeviceToDeviceRelationship
                            .builder()
                            .srcDeviceId(srcDevice.getId())
                            .destDeviceId(firewallDevice.getId())
                            .build()));
            srcDstIdentities.getLeft().ifPresent(srcIdentity -> identityToDeviceRelationships.add(
                    IdentityToDeviceRelationship
                            .builder()
                            .deviceId(srcDevice.getId())
                            .identityId(srcIdentity.getId())
                            .build()));
        });
        srcDstDevices.getRight().ifPresent(destDevice -> {
            inventoryItems.add(destDevice);
            firewall.ifPresent(firewallDevice -> deviceToDeviceRelationships.add(
                    DeviceToDeviceRelationship
                            .builder()
                            .srcDeviceId(firewallDevice.getId())
                            .destDeviceId(destDevice.getId())
                            .build()));
            srcDstIdentities.getRight().ifPresent(destIdentity -> identityToDeviceRelationships.add(
                    IdentityToDeviceRelationship
                            .builder()
                            .deviceId(destDevice.getId())
                            .identityId(destIdentity.getId())
                            .build()));
        });
        firewall.ifPresent(inventoryItems::add);
        srcDstIdentities.getLeft().ifPresent(inventoryItems::add);
        srcDstIdentities.getRight().ifPresent(inventoryItems::add);

        return FlowInventory.builder()
                            .inventoryItems(inventoryItems)
                            .identityToDeviceRelationships(identityToDeviceRelationships)
                            .deviceToDeviceRelationships(deviceToDeviceRelationships)
                            .build();
    }

    private Pair<Optional<InventoryItem>, Optional<InventoryItem>> extractDevicesFromFlow(final FlowValue flowValue) {
        final Optional<InventoryItem> sourceDevice = extractDeviceFromFlow(flowValue, flowValue.getSrcIP(),
                flowValue.getSourceHostName(), flowValue.getSourceMACAddress());
        final Optional<InventoryItem> destinationDevice = extractDeviceFromFlow(flowValue, flowValue.getDestIP(),
                flowValue.getDestinationHostName(), flowValue.getDestinationMACAddress());
        return Pair.of(sourceDevice, destinationDevice);
    }

    private Optional<InventoryItem> extractDeviceFromFlow(final FlowValue flowValue, final String ip, final String hostName,
                                                 final String macAddress) {
        return inventoryItemFromFlow(
                flowValue.getIllumioTenantId(),
                ip,
                InventoryObjectType.DEVICE,
                InventoryItemType.DEVICE,
                DeviceMetadata.builder()
                              .hostName(hostName)
                              .ip(ip)
                              .macAddress(macAddress)
                              .build());
    }

    private Optional<InventoryItem> extractFirewallFromFlow(final FlowValue flowValue) {
        return inventoryItemFromFlow(
                flowValue.getIllumioTenantId(),
                flowValue.getDeviceName(),
                InventoryObjectType.DEVICE,
                InventoryItemType.FIREWALL,
                FirewallDeviceMetadata.builder()
                                      .hostName(flowValue.getDeviceName())
                                      .ip(flowValue.getDeviceAddress())
                                      .macAddress(flowValue.getDeviceMACAddress())
                                      .externalId(flowValue.getDeviceExternalId())
                                      .version(flowValue.getDeviceVersion())
                                      .vendor(flowValue.getDeviceVendor())
                                      .product(flowValue.getDeviceProduct())
                                      .build());
    }

    private Pair<Optional<InventoryItem>, Optional<InventoryItem>> extractIdentitiesFromFlow(final FlowValue flowValue) {
        final Optional<InventoryItem> sourceIdentity = extractIdentityFromFlow(flowValue, flowValue.getSrcUserId(),
                flowValue.getSrcUserName(), flowValue.getSourceUserPrivileges());
        final Optional<InventoryItem> destinationIdentity = extractIdentityFromFlow(flowValue, flowValue.getDestUserId(),
                flowValue.getDestUserName(), flowValue.getDestinationUserPrivileges());
        return Pair.of(sourceIdentity, destinationIdentity);
    }

    private Optional<InventoryItem> extractIdentityFromFlow(final FlowValue flowValue, final String userId,
                                                  final String userName, final String userPrivileges) {
        return inventoryItemFromFlow(
                flowValue.getIllumioTenantId(),
                userName,
                InventoryObjectType.IDENTITY,
                InventoryItemType.USER,
                IdentityMetadata.builder()
                                .userId(userId)
                                .userName(userName)
                                .userPrivileges(userPrivileges)
                                .build());
    }

    @SneakyThrows
    private Optional<InventoryItem> inventoryItemFromFlow(final String tenantId,
                                                final String entityId,
                                                final InventoryObjectType superType,
                                                final InventoryItemType type,
                                                final Object data) {
        if (tenantId == null || entityId == null || type == null) {
            return Optional.empty();
        }
        return Optional.of(new InventoryItem(tenantId, entityId, superType, type, objectMapper.writeValueAsString(data)));
    }

}

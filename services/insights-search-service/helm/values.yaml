# Default values for iss.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# This will set the replicaset count more information can be found here: https://kubernetes.io/docs/concepts/workloads/controllers/replicaset/
replicaCount: 1

# This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
image:
  repositoryBase: "illum.azurecr.io/"
  repositoryName: "insights-search-service"
  pullPolicy: Always
  tag: # value given at helm deployment

# This is for the secretes for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imagePullSecrets: []
# This is to override the chart name.
nameOverride: ""
fullnameOverride: ""

# This section builds out the service account more information can be found here: https://kubernetes.io/docs/concepts/security/service-accounts/
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# This is for setting Kubernetes Annotations to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}
# This is for setting Kubernetes Labels to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

jwt:
  secret: _DO_NOT_COMMIT_ # picked from argo config, defined in vault

ports:
  - name: rest
    port: 8081

servicePorts:
  - name: rest
    podPort: rest
    servicePort: 8081

service:
  type: ClusterIP

ingress:
  tlsSecretName: "iss-tls"
  annotations: {}
  ingressClassName: "nginx"
  enabled: false
  certManager:
    enabled: false
    clusterIssuer: "cert-manager-letsencrypt-prod-route53"
  fqdn: ""
  path: "/api/v1/search"
  proxy_fqdn: ""

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# This section is for setting up autoscaling more information can be found here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

logging:
  level:
    ROOT: INFO

insightsConfig:
  kustoConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    rawTable: DecoratedFlows
    aggregatedTable: DecoratedFlows
    useIngestionTime: true
    isManagedIdentity: false
    azureClientId: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
    azureClientSecret: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
    azureTenantId: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
  riskyServiceConfig:
    riskyServiceFilePath: "classpath:risky_services.csv"
  apiConfig:
    authKey: AIzaSyCwEro-wQ6YUNcA1ozA9FQev-DyJp3t2EQ
    enableAuth: false
  jwtConfig:
    enableJwt: false
  redisConfig:
    host: sunnyvale-cloudsecure-admin-1-redis-insightssearch.redis.cache.windows.net
    port: 6380
    password: _DO_NOT_COMMIT_ # picked from argo config, defined in vault
    useSsl: true
    commandTimeoutMs: 1000
    ttlInMinutes: 60
    enableLandingPageCache: false
    inClusterMode: true
    clusterHosts: sunnyvale-cloudsecure-admin-1-redis-insightssearch.redis.cache.windows.net:6380

  pagination:
    maxPageSize: 1000
    defaultPageSize: 25
    defaultPageNumber: 1
    topResultHardLimit: 1000 # max limit for retrieve when NOT use pagination
    enableScatterGather: false

jvmOptions: "-Xms2g -Xmx4g -XX:+CrashOnOutOfMemoryError"


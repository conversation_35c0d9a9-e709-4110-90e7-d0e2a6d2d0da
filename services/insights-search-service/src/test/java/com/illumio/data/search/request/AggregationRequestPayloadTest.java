package com.illumio.data.search.request;

import com.illumio.data.model.AggregationField;
import com.illumio.data.model.AggregationField.Filters;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.search.model.AggregationRequestPayload;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class AggregationRequestPayloadTest {

    @Test
    public void testKQL(){
        AggregationField af = AggregationField.builder()
            .aggregatedTime("ingestion_time()")
            .aggregatedBy(List.of("sent_bytes", "flows"))
            .stepNumber(1)
            .stepUnit("hours")
            .filters(List.of(
                Filters.builder().name("source_ip").value("*******").build(),
                Filters.builder().name("destination_ip").value("*******").build()
            ))
            .build();

        AggregationRequestPayload payload = new AggregationRequestPayload();
        payload.setAggregationField(af);
        payload.setCurrentTimeFrame(TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build());

        String kql = payload.buildKQL();

        String expected = """
let Totalsent_bytes = null\s
| where IllumioTenantId == 'null'\s
 | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')| where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')\s
| where ((SrcIP  in  ( '*******' )) and (DestIP  in  ( '*******' )))\s
| project SentBytes, SrcIP, DestIP\s
| make-series Totalsent_bytes=sum(SentBytes) default=0 on ingestion_time() from datetime('2025-01-01T00:00:00Z') to datetime('2025-01-02T00:00:00Z') step 1h by SrcIP, DestIP;\s
let Totalflows = null\s
| where IllumioTenantId == 'null'\s
 | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')| where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')\s
| where ((SrcIP  in  ( '*******' )) and (DestIP  in  ( '*******' )))\s
| project FlowCount, SrcIP, DestIP\s
| make-series Totalflows=sum(FlowCount) default=0 on ingestion_time() from datetime('2025-01-01T00:00:00Z') to datetime('2025-01-02T00:00:00Z') step 1h by SrcIP, DestIP;\s
union Totalsent_bytes, Totalflows;
""";

        Assertions.assertEquals(expected.replaceAll("\\s+", ""),
            kql.replaceAll("\\s+", ""));
    }
}

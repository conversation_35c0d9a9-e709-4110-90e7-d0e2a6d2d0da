package com.illumio.data.search.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.illumio.data.model.AggregationField;
import com.illumio.data.model.AggregationField.Filters;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.search.model.AggregationRequestPayload;
import com.illumio.data.search.response.annotator.AggregationAnnotator;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class AggregationAnnotatorTest {
    private AggregationRequestPayload getPayload(){
        AggregationField af = AggregationField.builder()
            .aggregatedTime("ingestion_time()")
            .aggregatedBy(List.of("sent_bytes", "flows"))
            .stepNumber(1)
            .stepUnit("days")
            .filters(List.of(
                Filters.builder().name("source_ip").value("**********").build(),
                Filters.builder().name("destination_ip").value("**************").build()
            ))
            .build();

        AggregationRequestPayload payload = new AggregationRequestPayload();
        payload.setAggregationField(af);
        payload.setCurrentTimeFrame(
            TimeFrame.builder().startTime("2025-03-27T00:00:00Z").endTime("2025-03-29T00:00:00Z").build());

        payload.setTenantId("af84345d-0f7a-43ad-b19f-dcce53af2b9d");
        payload.setTableName("DecoratedFlows1");
        payload.setUseIngestionTime(true);
        System.out.println(payload.buildKQL());
        return payload;
    }

    /**
     * **********	**************	[10,20]	["2025-03-27T00:00:00.0000000Z","2025-03-28T00:00:00.0000000Z"]
     * **********	**************		    ["2025-03-27T00:00:00.0000000Z","2025-03-28T00:00:00.0000000Z"]	[420,0]
     *
     * @return
     */
    private SearchResponse getOriginalResponse() {
        SearchResponse response = new SearchResponse();

        response.setColumns(List.of("source_ip", "destination_ip", "Totalsent_bytes", "ingestion_time", "Totalflows"));

        List<Object> row1 = new ArrayList<>();
        row1.add("**********");
        row1.add("**************");

        ObjectMapper mapper = new ObjectMapper();

        ArrayNode intArrayNode = mapper.createArrayNode();
        intArrayNode.add(10);
        intArrayNode.add(20);

        row1.add(intArrayNode);

        // Create ArrayNode with date strings
        ArrayNode dateArrayNode = mapper.createArrayNode();
        dateArrayNode.add("2025-03-27T00:00:00.0000000Z");
        dateArrayNode.add("2025-03-28T00:00:00.0000000Z");

        row1.add(dateArrayNode);
        row1.add(null); // flows is null
        // row 1 done

        List<Object> row2 = new ArrayList<>();
        row2.add("**********");
        row2.add("**************");

        row2.add(null); // sent_bytes is null

        // Create ArrayNode with date strings
        dateArrayNode = mapper.createArrayNode();
        dateArrayNode.add("2025-03-27T00:00:00.0000000Z");
        dateArrayNode.add("2025-03-28T00:00:00.0000000Z");

        row2.add(dateArrayNode);

        intArrayNode = mapper.createArrayNode();
        intArrayNode.add(420);
        intArrayNode.add(0);

        row2.add(intArrayNode);
        // row 2 done


        List<List<Object>> data = new ArrayList<>();
        data.add(row1);
        data.add(row2);
        response.setData(data);
        return response;
    }

    @Test
    public void testAnnotate() throws JsonProcessingException {
        AggregationRequestPayload payload = getPayload();
        SearchResponse response = getOriginalResponse();
        SearchResponse annotated = new AggregationAnnotator().annotate(payload, response);

        Assertions.assertEquals(4, annotated.getColumns().size());
        Assertions.assertEquals("source_ip", annotated.getColumns().get(0));
        Assertions.assertEquals("destination_ip", annotated.getColumns().get(1));
        Assertions.assertEquals("aggregate_field", annotated.getColumns().get(2));
        Assertions.assertEquals("timeseries", annotated.getColumns().get(3));

        Assertions.assertTrue(annotated.getData().size() == 2);
        List<Object> row1 = annotated.getData().get(0);
        Assertions.assertEquals(4, row1.size());
        Assertions.assertEquals("**********", row1.get(0));
        Assertions.assertEquals("**************", row1.get(1));
        Assertions.assertEquals("sent_bytes", row1.get(2));
        Assertions.assertInstanceOf(List.class, row1.get(3));
        Assertions.assertEquals(2, ((List) row1.get(3)).size());
        List<Object> aggData = (List<Object>)row1.get(3);
        Assertions.assertInstanceOf(List.class, aggData.get(0));
        Assertions.assertInstanceOf(List.class, aggData.get(1));
        List<Object> aggData0 = (List<Object>)aggData.get(0);
        Assertions.assertEquals("2025-03-27T00:00:00.0000000Z", aggData0.get(0));
        Assertions.assertTrue(aggData0.get(1) instanceof Long && (Long)aggData0.get(1) == 10);

        List<Object> aggData1 = (List<Object>)aggData.get(1);
        Assertions.assertEquals("2025-03-28T00:00:00.0000000Z", aggData1.get(0));
        Assertions.assertTrue(aggData1.get(1) instanceof Long && (Long)aggData1.get(1) == 20);


        List<Object> row2 = annotated.getData().get(1);
        Assertions.assertEquals(4, row2.size());
        Assertions.assertEquals("**********", row2.get(0));
        Assertions.assertEquals("**************", row2.get(1));
        Assertions.assertEquals("flows", row2.get(2));
        Assertions.assertInstanceOf(List.class, row2.get(3));
        Assertions.assertEquals(2, ((List) row2.get(3)).size());

        aggData = (List<Object>)row2.get(3);
        Assertions.assertInstanceOf(List.class, aggData.get(0));
        Assertions.assertInstanceOf(List.class, aggData.get(1));
        aggData0 = (List<Object>)aggData.get(0);
        Assertions.assertEquals("2025-03-27T00:00:00.0000000Z", aggData0.get(0));
        Assertions.assertTrue(aggData0.get(1) instanceof Long && (Long)aggData0.get(1) == 420);

        aggData1 = (List<Object>)aggData.get(1);
        Assertions.assertEquals("2025-03-28T00:00:00.0000000Z", aggData1.get(0));
        Assertions.assertTrue(aggData1.get(1) instanceof Long && (Long)aggData1.get(1) == 0);

        // Jackson ObjectMapper
        ObjectMapper mapper = new ObjectMapper();

        // Convert object to JSON string
        String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(annotated);

        // Print the JSON
        /**
         {
         "currentTimeFrame" : null,
         "columns" : [ "source_ip", "destination_ip", "aggregate_field", "timeseries" ],
         "columnTypes" : [ "string", "string", "string", "[string, number][]" ],
         "columnDisplayNames" : [ "Source IP", "Destination IP", "Aggregate Field", "Time Series" ],
         "data" : [ [ "**********", "**************", "sent_bytes", [ [ "2025-03-27T00:00:00.0000000Z", 10 ], [ "2025-03-28T00:00:00.0000000Z", 20 ] ] ], [ "**********", "**************", "flows", [ [ "2025-03-27T00:00:00.0000000Z", 420 ], [ "2025-03-28T00:00:00.0000000Z", 0 ] ] ] ],
         "sortByFields" : null,
         "pagination" : null
         }
         */
        System.out.println(json);
    }
}

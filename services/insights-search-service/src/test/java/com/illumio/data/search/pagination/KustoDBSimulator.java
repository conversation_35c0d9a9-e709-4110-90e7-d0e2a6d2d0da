package com.illumio.data.search.pagination;


import static org.mockito.Mockito.when;

import com.microsoft.azure.kusto.data.KustoResultColumn;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.junit.jupiter.api.Assertions;

public class KustoDBSimulator {
    public final static int PAGE_SIZE = 3;
    private static List<String> getDestIPList(){
        // [ip1, ip3, ip5, ip7, ip9, ip11, ip13, ip15, ip17, ip19, ip21, ip23, ip25, ip27, ip29]
        List<String> result = new ArrayList<>();
        for(int i=0; i<15; i++){
            result.add("ip" + (2*i+1));
        }

        return result;
    }

    private static List<Integer> getFlowCountList(){
        // [41000, 41000, 41000, 49000, 49000, 49000, 49000, 49000, 49000, 49000, 49000, 50000, 51000, 52000, 53000]
        List<Integer> result = new ArrayList<>();
        for(int i=0; i<3; i++){
            result.add(41000);
        }

        for(int i=0; i<8; i++){
            result.add(49000);
        }

        result.addAll(List.of( 50000, 51000, 52000, 53000));
        return result;
    }

    private static KustoResultColumn[] getColumns() {
        List<KustoResultColumn> columns = new ArrayList<>();

        columns.add(new KustoResultColumn("DestIP", "string", 0));
        columns.add(new KustoResultColumn("FlowCount", "int", 1));

        return columns.toArray(new KustoResultColumn[columns.size()]);
    }

    public static KustoResultSetTable createDBResult(boolean revOrder, int minValue, int maxValue, int rowCount){
        List<String> allIps = getDestIPList();
        List<Integer> allFlows = getFlowCountList();
        if (revOrder){
            Collections.reverse(allFlows);
            Collections.reverse(allIps);
        }

        List<String> Ips = new ArrayList<>();
        List<Integer> flows = new ArrayList<>();
        int count = 0;
        for(int i=0; i<allFlows.size(); i++){
            int flow = allFlows.get(i);
            if (flow >= minValue && flow <= maxValue){
                Ips.add(allIps.get(i));
                flows.add(flow);
                count++;
            }

            if (count >= rowCount){break;}
        }

        // Create mock
        KustoResultSetTable resultSet = Mockito.mock(KustoResultSetTable.class);

        when(resultSet.getColumns()).thenReturn(getColumns());

        // Use an AtomicInteger to simulate cursor/index
        AtomicInteger index = new AtomicInteger(0);

        // Simulate next() returning true while we have more data
        when(resultSet.next()).thenAnswer((Answer<Boolean>) invocation -> index.incrementAndGet() <= Ips.size());

        AtomicInteger index2 = new AtomicInteger(-1);
        when(resultSet.getObject(0)).thenAnswer(invocation -> {
            index2.incrementAndGet();
            return Ips.get(index2.get());
        });

        AtomicInteger index3 = new AtomicInteger(-1);
        when(resultSet.getObject(1)).thenAnswer(invocation -> {
            index3.incrementAndGet();
            return flows.get(index3.get());
        });

        return resultSet;
    }

    protected static Pair<KustoResultColumn[], List<List<Object>>> transferDBResult(KustoResultSetTable content){
        KustoResultColumn[] columns = content.getColumns();
        List<List<Object>> data = new ArrayList<>();
        while (content.next()) {
            List<Object> dataRow = new ArrayList<>();
            for (int i = 0; i < columns.length; i++) {

                dataRow.add(content.getObject(i));
            }
            data.add(dataRow);
        }

        KustoResultColumn[] origin = getColumns();

        for(int i=0; i<columns.length; i++){
            Assertions.assertEquals(columns[i].getColumnName(), origin[i].getColumnName());
            Assertions.assertEquals(columns[i].getColumnType(), origin[i].getColumnType());
        }

        return Pair.of(columns, data);
    }

    @Test
    public void testPage1(){
        KustoResultSetTable firstPage = createDBResult(false, Integer.MIN_VALUE, Integer.MAX_VALUE, PAGE_SIZE);

        Pair<KustoResultColumn[], List<List<Object>>> transferResult = transferDBResult(firstPage);
        List<List<Object>> data = transferResult.getValue();

        Assertions.assertEquals(3, data.size());
        Assertions.assertEquals("ip1", data.get(0).get(0));
        Assertions.assertEquals(41000, data.get(0).get(1));

        Assertions.assertEquals("ip3", data.get(1).get(0));
        Assertions.assertEquals(41000, data.get(1).get(1));

        Assertions.assertEquals("ip5", data.get(2).get(0));
        Assertions.assertEquals(41000, data.get(2).get(1));

    }

    @Test
    public void testLastPage(){
        KustoResultSetTable content = createDBResult(true, Integer.MIN_VALUE, Integer.MAX_VALUE, PAGE_SIZE+1);

        Pair<KustoResultColumn[], List<List<Object>>> transferResult = transferDBResult(content);
        List<List<Object>> data = transferResult.getValue();

        Assertions.assertEquals(4, data.size());
        Assertions.assertEquals("ip29", data.get(0).get(0));
        Assertions.assertEquals(53000, data.get(0).get(1));

        Assertions.assertEquals("ip27", data.get(1).get(0));
        Assertions.assertEquals(52000, data.get(1).get(1));

        Assertions.assertEquals("ip25", data.get(2).get(0));
        Assertions.assertEquals(51000, data.get(2).get(1));

        Assertions.assertEquals("ip23", data.get(3).get(0));
        Assertions.assertEquals(50000, data.get(3).get(1));

    }

    @Test
    public void testMiddle(){
        KustoResultSetTable content = createDBResult(false, 49000, 50000, PAGE_SIZE);

        Pair<KustoResultColumn[], List<List<Object>>> transferResult = transferDBResult(content);
        List<List<Object>> data = transferResult.getValue();

        Assertions.assertEquals(3, data.size());
        Assertions.assertEquals("ip7", data.get(0).get(0));
        Assertions.assertEquals(49000, data.get(0).get(1));

        Assertions.assertEquals("ip9", data.get(1).get(0));
        Assertions.assertEquals(49000, data.get(1).get(1));

        Assertions.assertEquals("ip11", data.get(2).get(0));
        Assertions.assertEquals(49000, data.get(2).get(1));
    }
}

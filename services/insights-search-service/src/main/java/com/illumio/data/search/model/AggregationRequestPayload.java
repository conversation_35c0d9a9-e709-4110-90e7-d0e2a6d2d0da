package com.illumio.data.search.model;

import com.illumio.data.model.AggregationField;
import com.illumio.data.model.AggregationField.Filters;
import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.generalFilters.BaseFilter;
import com.illumio.data.model.generalFilters.InFilter;
import com.illumio.data.model.generalFilters.LogicFilter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@Setter
/**
 * class to encapsulate aggregation related fields
 */
public class AggregationRequestPayload extends SearchRequestPayload {
    @Getter
    private AggregationField aggregationField;

    /**
     let Totalsent_bytes = DecoratedFlows1
     | where IllumioTenantId == 'af84345d-0f7a-43ad-b19f-dcce53af2b9d'
     | where ingestion_time() >= datetime('2025-03-27T08:00:00')| where ingestion_time() <= datetime('2025-04-01T07:00:00')
     | where ((SrcIP  in  ( '**********' )) and (DestIP  in  ( '**************' )))
     | project SentBytes, SrcIP, DestIP
     | make-series Totalsent_bytes=sum(SentBytes) default=0 on ingestion_time() from datetime('2025-03-27T08:00:00') to datetime('2025-04-01T07:00:00') step 1h by SrcIP, DestIP;
     let Totalflows = DecoratedFlows1
     | where IllumioTenantId == 'af84345d-0f7a-43ad-b19f-dcce53af2b9d'
     | where ingestion_time() >= datetime('2025-03-27T08:00:00')| where ingestion_time() <= datetime('2025-04-01T07:00:00')
     | where ((SrcIP  in  ( '**********' )) and (DestIP  in  ( '**************' )))
     | project FlowCount, SrcIP, DestIP
     | make-series Totalflows=sum(FlowCount) default=0 on ingestion_time() from datetime('2025-03-27T08:00:00') to datetime('2025-04-01T07:00:00') step 1h by SrcIP, DestIP;
     union Totalsent_bytes, Totalflows;

     *
     * @return
     */
    @Override
    public String buildKQL() {
        this.getCurrentTimeFrame().setUseIngestionTime(this.useIngestionTime);
        // single aggregated by, no need to union
        if (this.aggregationField.getAggregatedBy().size() == 1){
            String byField = this.aggregationField.getAggregatedBy().get(0);
            return buildSingleAggregationKQL(byField);
        }

        List<String> buffer = new ArrayList<>();
        List<String> unionNames = new ArrayList<>();
        for(String byField : this.aggregationField.getAggregatedBy()){
            String unionName = this.getSumName(byField);
            unionNames.add(unionName);
            buffer.add("let %s = %s" .formatted(unionName, buildSingleAggregationKQL(byField)));
        }
        buffer.add("union %s;".formatted(String.join(", ", unionNames)));
        return String.join(" \n", buffer);
    }

    public String getSumName(String sumByField){
        return "Total" + sumByField;
    }

    /**
     Example:

     DecoratedFlows1
     | where IllumioTenantId == 'af84345d-0f7a-43ad-b19f-dcce53af2b9d'
     | where ingestion_time() >= datetime('2025-03-27T08:00:00')| where ingestion_time() <= datetime('2025-04-01T07:00:00')
     | where ((SrcIP  in  ( '**********' )) and (DestIP  in  ( '**************' )))
     | project SentBytes, SrcIP, DestIP
     | make-series Totalsent_bytes=sum(SentBytes) default=0 on ingestion_time() from datetime('2025-03-27T08:00:00') to datetime('2025-04-01T07:00:00') step 1h by SrcIP, DestIP;


     * @param sumByField
     * @return
     */
    private String buildSingleAggregationKQL(String sumByField) {
        List<String> buffer = new ArrayList<>();
        buffer.add(this.tableName);
        buffer.add("| where IllumioTenantId == '%s'".formatted(tenantId));

        if (this.getCurrentTimeFrame() != null) {
            buffer.add(this.getCurrentTimeFrame().buildKQL());
        }

        // setup filters based on matchers
        if (!this.aggregationField.getFilters().isEmpty()) {
            List<BaseFilter> filters = new ArrayList<>();
            for (Filters matcher : this.aggregationField.getFilters()) {
                InFilter inFilter = new InFilter();
                inFilter.setField(matcher.getName());
                inFilter.setValues(List.of(matcher.getValue()));
                filters.add(inFilter);
            }

            LogicFilter logic = new LogicFilter();
            logic.setAndRelation(true);
            logic.setFilters(filters);

            buffer.add("| where %s".formatted(logic.buildKQL()));
        }

        // add projection fields
        ProjectFields projectFields = new ProjectFields();
        List<String> projects = new ArrayList<>();

        this.addProjectFields(projects, sumByField);

        if (this.aggregationField.getFilters() != null) {
            this.aggregationField.getFilters().stream()
                .map(Filters::getName)
                .forEach(projects::add)
                ;
        }
        projectFields.setProjectFields(projects);

        buffer.add(projectFields.buildKQL());

        // add aggregation
        String sumBy = this.getSumBy(sumByField);

        String groupBy = this.aggregationField.getFilters().stream()
            .map(Filters::getName)
            .map(Fields::getTableColumnNameByFieldKey)
            .collect(
                Collectors.joining(", "));

        String sumName = getSumName(sumByField);
        String fromTo = this.getCurrentTimeFrame().getFromToString();
        String query = String.format(
            "| make-series %s=sum(%s) default=0 on ingestion_time() %s step %s by %s;",
            sumName, sumBy, fromTo, this.aggregationField.getStep(), groupBy
        );
        buffer.add(query);


        return String.join(" \n", buffer);
    }

    private void addProjectFields(List<String> projects, String sumByField){
        // for bytes, need to sum both sent and received bytes
        if (sumByField.equals(Fields.BYTES.getFieldKey())) {
            projects.add(Fields.SENT_BYTES.getFieldKey());
            projects.add(Fields.RECEIVED_BYTES.getFieldKey());
        } else {
            projects.add(sumByField);
        }
    }

    private String getSumBy(String sumByField){
        // for bytes, need to sum both sent and received bytes
        if (sumByField.equals(Fields.BYTES.getFieldKey())) {
            return "%s + %s".formatted(Fields.SENT_BYTES.getTableColumnName(), Fields.RECEIVED_BYTES.getTableColumnName() );
        }
        return Fields.getTableColumnNameByFieldKey(sumByField);
    }
}

package com.illumio.data.search.response;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.SortByFields;
import com.illumio.data.search.model.SearchRequestPayload;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SearchResponseCombiner {

    // Do-nothing comparator (always returns 0)
    public static final Comparator<List<Object>> doNothingComparator = (a, b) -> 0;

    /**
     * Gather part in the Scatter/Gather, need to pick top rows from the
     * sub results from Scatter
     * @param searchResponseList: partial result from sub query
     * @param payload: original payload,
     * @return: combined result with content limited to input limit
     */
    public static SearchResponse combine(List<SearchResponse> searchResponseList, SearchRequestPayload payload) {
        int limit = payload.getPagination().getTopResultHardLimit();
        if (searchResponseList == null || searchResponseList.isEmpty()) {return null;}

        if (searchResponseList.size() == 1) {return searchResponseList.get(0);}
        SearchResponse template = searchResponseList.get(0);

        SearchResponse response = new SearchResponse();
        response.setColumns(template.getColumns());
        response.setColumnTypes(template.getColumnTypes());
        response.setColumnDisplayNames(template.getColumnDisplayNames());

        PriorityQueue<List<Object>> combinedHeap = new PriorityQueue<>(
            getComparator(template)
        );

        for(SearchResponse r : searchResponseList) {
            for(List<Object> row : r.getData()) {
                combinedHeap.add(row);
                if (combinedHeap.size() > limit) {
                    combinedHeap.poll();
                }
            }
        }

        List<List<Object>> combinedList = new ArrayList<>(combinedHeap.size());
        while(!combinedHeap.isEmpty()) {
            // need to add in reverse order
            combinedList.add(0, combinedHeap.poll());
        }

        response.setData(combinedList);

        // need to use the time frame from the original payload which include the
        // actual start and end time
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setSortByFields(template.getSortByFields());

        // to avoid UI changes, make the pagination looks as if the same
        // i.e. pageNumber 1, rowLimit 1000, totalPage: 1,
        response.setPagination(Pagination.builder()
                .rowLimit(limit)
                .pageNumber(1)
                .totalPages(1)
                .topResultHardLimit(limit)
                .topResultRows(combinedList.size())
                .enableScatterGather(true)
            .build());

        return response;
    }


    /**
     * Based on the sort logic, build the comparator
     * @param searchResponse: the result from sub query which contains the sorting logic and return columns meta data
     * @return: the comparator based on the sorting logic
     */
    public static Comparator<List<Object>> getComparator(SearchResponse searchResponse) {
        // all the columns are in fields name, NOT DB column name
        List<String> columns = searchResponse.getColumns();
        List<SortByFields> sortByFields = searchResponse.getSortByFields();
        if (sortByFields == null || sortByFields.isEmpty()) {return doNothingComparator;}

        Map<SortByFields, Integer> sortByFieldsIndex = new HashMap<>();

        for(SortByFields sortByField : sortByFields) {
            String adjustedFieldName = sortByField.getAdjustedField();
            if (adjustedFieldName == null) {
                log.error("Adjusted field name is null for sortByField {}", sortByField);
                continue;
            }

            int index = IntStream.range(0, columns.size())
                .filter(i -> adjustedFieldName.equals(columns.get(i)))
                .findFirst()
                .orElse(-1);

            if (index < 0) {
                log.error("Can not find sorting columns for {} with adjusted name {}", sortByField, adjustedFieldName);
            } else {
                sortByFieldsIndex.put(sortByField, index);
            }
        }

        if (sortByFieldsIndex.size() != sortByFields.size()) {
            log.error("Sorting logic does NOT match the result result metadata {}", columns);
            return doNothingComparator;
        }

        return (list1, list2) ->{
            if (list1 == null || list2 == null || list1.size() != list2.size()) {
                return 0;
            }

            if (list1.size() != columns.size()) { return 0; }

            // need to maintain the order of the sorted fields
            for(SortByFields field : sortByFields) {
                int index = sortByFieldsIndex.get(field);
                boolean asc = field.getOrder().equalsIgnoreCase("asc");

                // index out of bound
                if (list1.size() < index+1) {
                    log.error("Sorting logic index out of bound {}", index);
                    return 0;
                }
                Object obj1 = list1.get(index);
                Object obj2 = list2.get(index);
                if (obj1 instanceof Comparable && obj2 instanceof Comparable) {
                    int tmp = ((Comparable)obj1).compareTo(obj2);
                    if (tmp != 0) {
                        return asc? -tmp: tmp;
                    }
                }
            }

            return 0;
        };
    }
}

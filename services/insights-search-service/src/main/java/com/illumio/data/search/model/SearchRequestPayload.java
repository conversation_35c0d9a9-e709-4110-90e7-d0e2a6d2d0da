package com.illumio.data.search.model;

import com.illumio.data.model.FilterGroup;
import com.illumio.data.model.Filters;
import com.illumio.data.model.InOutBoundFilterDecorator;
import com.illumio.data.model.KQLProvider;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.SrcDestExtLabelFilter;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.TimeRangeSplitter;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.deepPagination.DpTargetPageInfor;
import com.illumio.data.model.generalFilters.BaseFilter;
import com.illumio.data.model.generalFilters.IpOverviewFilter;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;

@NoArgsConstructor
@Setter
public class SearchRequestPayload extends RequestPayload implements KQLProvider {
    @Getter
    private ProjectFields projectFields;

    protected String tableName;

    // IllumioTenantId
    protected String tenantId;

    protected boolean useIngestionTime;

    @Getter
    private IpOverviewFilter.IpOverviewUnit ipOverviewUnit;

    public static final String TABLE_NAME = "DecoratedFlows";

    public SearchRequestPayload(
            TimeFrame currentTimeFrame,
            TimeFrame comparisonTimeFrame,
            List<Filters> filters,
            List<SortByFields> sortByFields,
            Pagination pagination,
            List<FilterGroup> filterGroups,
            ProjectFields projectFields,
            BaseFilter baseFilter) {
        super(
                currentTimeFrame,
                comparisonTimeFrame,
                filters,
                sortByFields,
                pagination,
                filterGroups,
                baseFilter);
        this.projectFields = projectFields;
        this.tableName = TABLE_NAME;
        this.useIngestionTime = false;
    }

    /**
     * Used for scatter and gather logic
     * @return the deep copy of this object
     */
    public SearchRequestPayload deepCopy() {
        SearchRequestPayload copy = new SearchRequestPayload(
            this.getCurrentTimeFrame(),
            this.getComparisonTimeFrame(),
            this.getFilters(),
            this.getSortByFields(),
            this.getPagination(),
            this.getFilterGroups(),
            this.getProjectFields(),
            this.getBaseFilter()
        );

        copy.tableName = this.tableName;
        copy.tenantId = this.tenantId;
        copy.useIngestionTime = this.useIngestionTime;

        return copy;
    }

    /**
     * Partition the payload based on currentTimeFrame and clone the other information
     * @param unit: Time unit
     * @return: partitioned payload based on input. All the data will be the same for the list, except
     * the time range
     */
    public List<SearchRequestPayload> partitionByUnit(ChronoUnit unit) {
        List<SearchRequestPayload> result = new ArrayList<>();
        String startTime = this.getCurrentTimeFrame().getStartTime();
        String endTime = this.getCurrentTimeFrame().getEndTime();

        List<Pair<String, String>> pairs = TimeRangeSplitter.splitIntoBuckets(startTime, endTime, unit);
        for(Pair<String, String> pair : pairs) {
            TimeFrame timeFrame = TimeFrame
                .builder()
                .startTime(pair.getKey())
                .endTime(pair.getValue())
                .useIngestionTime(this.getCurrentTimeFrame().isUseIngestionTime())
                .build();

            SearchRequestPayload payload = this.deepCopy();
            payload.setCurrentTimeFrame(timeFrame);

            result.add(payload);
        }

        return result;
    }

    @Override
    public String buildKQL() {
        this.getCurrentTimeFrame().setUseIngestionTime(this.useIngestionTime);

        String kqlWithoutPagination = buildKQLWithoutPagination();

        if (this.getPagination() != null) {
            this.getPagination().setOverallKQLWithoutPagination(Optional.of(kqlWithoutPagination));
            return this.getPagination().buildKQL();
        } else {
            return kqlWithoutPagination;
        }
    }

    private String buildKQLWithoutPagination() {
        List<String> buffer = new ArrayList<>();
        buffer.add(this.tableName);
        buffer.add("| where IllumioTenantId == '%s'".formatted(tenantId));

        if (this.getCurrentTimeFrame() != null) {
            buffer.add(this.getCurrentTimeFrame().buildKQL());
        }

        // general filters override old version
        if (this.getBaseFilter() != null) {
            String kql = this.getBaseFilter().buildKQL();
            if (!kql.isEmpty()) {
                buffer.add("| where %s ".formatted(kql));
            }
        }
        else {
            if (this.getFilters() != null) {
                List<Filters> additionalFilters = new ArrayList<>();
                AtomicReference<Filters> destExtLabelCat = new AtomicReference<>();
                AtomicReference<Filters> destExtLabel = new AtomicReference<>();
                this.getFilters()
                        .forEach(
                                f -> {
                                    if (f.getCategoryName()
                                            .equalsIgnoreCase(
                                                    Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY
                                                            .getFieldKey())) {
                                        destExtLabelCat.set(f);
                                    } else if (f.getCategoryName()
                                            .equalsIgnoreCase(
                                                    Fields.DESTINATION_EXTERNAL_LABEL
                                                            .getFieldKey())) {
                                        destExtLabel.set(f);
                                    } else {
                                        additionalFilters.add(f);
                                    }
                                });

                if (destExtLabelCat.get() != null && destExtLabel.get() != null) {
                    buffer.add(
                            "| where %s "
                                    .formatted(
                                            SrcDestExtLabelFilter.adjustExtLabel(
                                                    destExtLabelCat.get(), destExtLabel.get())));
                }

                // decorator for in/out bound
                additionalFilters.forEach(
                        f -> buffer.add(new InOutBoundFilterDecorator(f).buildKQL()));
            }

            if (this.getFilterGroups() != null) {
                this.getFilterGroups().forEach(f -> buffer.add(f.buildKQL()));
            }
        }

        // deep pagination
        DpTargetPageInfor targetPage = this.getPagination().getTargetPage();
        if (targetPage != null && targetPage.getAdditionalFilter() != null) {
            buffer.add("| where %s ".formatted(targetPage.getAdditionalFilter().buildKQL()));
        }

        // sorting will be in opposite order when pagination in Reverse Traverse
        if (this.getSortByFields() != null) {
            buffer.add(
                SortByFields.buildKQLForMultiples(
                    this.getSortByFields().stream()
                        // possible reverse the order
                        .map(f -> new OppositeSortDecorator(f, this.getPagination()))
                        .collect(Collectors.toList())
                )
            );
        }

        if (this.projectFields != null) {
            ProjectFields decorator =
                    new ProjectFieldsServiceDecorator(
                            new ProjectFieldsSrcZoneDecorator(
                                    new ProjectFieldsDestZoneDecorator(this.projectFields)));

            ProjectFields decorator2 =
                    new ProjectFieldsBySortDecorator(decorator, this.getSortByFields());

            buffer.add(decorator2.buildKQL());
        } else {
            buffer.add(ProjectFields.buildKQLForAllAvailableColumns());
        }

        return String.join(" \n", buffer);
    }
}

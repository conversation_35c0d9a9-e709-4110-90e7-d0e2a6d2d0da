package com.illumio.data.search.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import java.util.Arrays;

@Configuration
public class GlobalCorsConfig {

    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();

        // Allow specific origins
        corsConfig.setAllowedOrigins(Arrays.asList(
                "https://graph.illum.io",
                "https://insights.redmond.ilabs.io",
                "https://insights.sunnyvale.ilabs.io",
                "https://console.sunnyvale.ilabs.io",
                "https://console.redmond.ilabs.io",
                "http://localhost:8081",
                "https://localhost:8443",
                "https://localhost:8479"
        ));

        // Allow all methods
        corsConfig.setAllowedMethods(Arrays.asList("*"));

        // Allow all headers
        corsConfig.setAllowedHeaders(Arrays.asList("*"));

        // Allow credentials
        corsConfig.setAllowCredentials(true);

        // Set max age
        corsConfig.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        return new CorsWebFilter(source);
    }
}

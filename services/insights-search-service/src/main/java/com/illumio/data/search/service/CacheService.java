package com.illumio.data.search.service;

import com.illumio.data.search.model.constants.MetricStatusLabel;
import com.illumio.data.search.model.constants.Metrics;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveRedisOperations;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
@Slf4j
public class CacheService {
    private final ReactiveRedisOperations<String, String> reactiveRedisOperations;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;

    public Mono<String> getSimpleValue(final String redisKey) {
        log.debug("Fetch key {} from external cache", redisKey);
        return reactiveRedisOperations
            .opsForValue()
            .get(redisKey)
            .flatMap(value -> {
                log.debug("External Cache Hit: Fetched value={} for key={} from external cache", value, redisKey);
                return Mono.just(value);
            })
            .switchIfEmpty(Mono.defer(() -> {
                log.debug("External Cache Miss: Value unavailable for key={}", redisKey);
                return Mono.empty();
            }))
            .onErrorResume(e -> {
                log.error("Error fetching Value from the external cache for key {} : {}", redisKey, e.getMessage());
                return Mono.empty();
            });
    }

    /**
     * return the field value from Redis HashKey and fieldKey,
     *
     * 127.0.0.1:6379> HSET tennant1:3_31:4_1 page1 "content of page1" page2 "content of page 2"
     * (integer) 2
     * 127.0.0.1:6379> HGETALL  tennant1:3_31:4_1
     * 1) "page1"
     * 2) "content of page1"
     * 3) "page2"
     * 4) "content of page 2"
     *
     * HashKey will be tennant1:3_31:4_1
     * filedKey will be page1 or page2
     */
    public Mono<String> getHashFieldValue(final String hashKey, final String fieldKey, String tenantID) {
        log.debug("Fetching hashKey={}, fieldKey={}", hashKey, fieldKey);
        Timer.Sample timer = Timer.start(meterRegistry);
        return reactiveRedisOperations
                .opsForHash()
                .get(hashKey, fieldKey)
                .flatMap(
                        value -> {
                            log.debug(
                                    "External Cache Hit: Fetched value={} for hashKey={}, fieldKey={}",
                                    value,
                                    hashKey,
                                    fieldKey);
                            if (value != null) {
                                metricRecordService.recordTimeAndCountMetrics(
                                    meterRegistry,
                                    timer,
                                    MetricStatusLabel.SUCCESS.getValue(),
                                    "Time taken to get hash field value",
                                    tenantID,
                                    Metrics.REDIS_QUERY_SUCCESS_LATENCY_METRIC.getValue(),
                                    Metrics.REDIS_HIT_COUNT.getValue());

                                return Mono.just(value.toString());
                            }

                            log.error(
                                    "External value is Null for hashKey={}, fieldKey={}",
                                    hashKey,
                                    fieldKey);

                            metricRecordService.recordTimeAndCountMetrics(
                                meterRegistry,
                                timer,
                                MetricStatusLabel.ERROR.getValue(),
                                "Time taken to get hash field value",
                                tenantID,
                                Metrics.REDIS_QUERY_FAIL_LATENCY_METRIC.getValue(),
                                Metrics.REDIS_GET_ERROR_COUNT.getValue());

                            return Mono.empty();
                        })
                .switchIfEmpty(
                        Mono.defer(
                                () -> {
                                    log.debug(
                                            "External Cache Miss: Value unavailable for hashKey={}, key={}",
                                            hashKey,
                                            fieldKey);
                                    metricRecordService.recordTimeAndCountMetrics(
                                            meterRegistry,
                                            timer,
                                            MetricStatusLabel.SUCCESS.getValue(),
                                            "Time taken to get hash field value",
                                            tenantID,
                                            Metrics.REDIS_QUERY_SUCCESS_LATENCY_METRIC.getValue(),
                                            Metrics.REDIS_MISS_COUNT.getValue());
                                    return Mono.empty();
                                }))
                .onErrorResume(
                        e -> {
                            log.error(
                                    "Error fetching Value for hashKey={}, fieldKey={} : {}",
                                    hashKey,
                                    fieldKey,
                                    e.getMessage());
                            metricRecordService.recordTimeAndCountMetrics(
                                    meterRegistry,
                                    timer,
                                    MetricStatusLabel.ERROR.getValue(),
                                    "Time taken to get hash field value",
                                    tenantID,
                                    Metrics.REDIS_QUERY_FAIL_LATENCY_METRIC.getValue(),
                                    Metrics.REDIS_GET_ERROR_COUNT.getValue());
                            return Mono.empty();
                        });
    }

    public Mono<Boolean> setHashFieldValue(final String tenantID, final String hashKey, final String fieldKey, final Object value) {
        return setHashFieldValue(tenantID, hashKey, fieldKey, value, 60L);
    }

    public Mono<Boolean> setHashFieldValue(final String tenantID, final String hashKey, final String fieldKey, final Object value, final long ttlInMinutes) {
        log.debug("Setting hashKey={}, fieldKey={}, fieldValue={}", hashKey, fieldKey, value);
        Timer.Sample timer = Timer.start(meterRegistry);
        return reactiveRedisOperations
            .opsForHash()
            .put(hashKey, fieldKey, value)
            .flatMap(
                result -> {
                    // Extends with TTL
                    Mono<Boolean>  r = reactiveRedisOperations.expire(hashKey, Duration.ofMinutes(ttlInMinutes));
                    metricRecordService.recordTimeAndCountMetrics(
                        meterRegistry,
                        timer,
                        MetricStatusLabel.SUCCESS.getValue(),
                        "Time taken to get hash field value",
                        tenantID,
                        Metrics.REDIS_QUERY_SUCCESS_LATENCY_METRIC.getValue(),
                        Metrics.REDIS_HIT_COUNT.getValue());
                    return  r;
                }
            )
            .onErrorResume(e -> {
                log.error("Error setting Value for hashKey={}, fieldKey={}", hashKey, fieldKey, e);
                metricRecordService.recordTimeAndCountMetrics(
                    meterRegistry,
                    timer,
                    MetricStatusLabel.ERROR.getValue(),
                    "Time taken to get hash field value",
                    tenantID,
                    Metrics.REDIS_QUERY_FAIL_LATENCY_METRIC.getValue(),
                    Metrics.REDIS_SET_ERROR_COUNT.getValue());

                return Mono.just(false);
            });
    }
}

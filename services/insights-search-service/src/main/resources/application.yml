logging:
  level:
    ROOT: INFO

jwt:
  secret: _DO_NOT_COMMIT_

server:
  port:
    8081

spring:
  application:
    name: search-service
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive

insights-config:
  kustoConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    rawTable: DecoratedFlows
    aggregatedTable: DecoratedFlows
    useIngestionTime: true
    isManagedIdentity: false
    azureClientId: _DO_NOT_COMMIT_
    azureClientSecret: _DO_NOT_COMMIT_
    azureTenantId: _DO_NOT_COMMIT_
  riskyServiceConfig:
    riskyServiceFilePath: "classpath:risky_services.csv"
  paginationConfig:
    maxPageSize: 100
    defaultPageSize: 25
    defaultPageNumber: 1
    topResultHardLimit: 1000
    enableScatterGather: false
  apiConfig:
    authKey: AIzaSyCwEro-wQ6YUNcA1ozA9FQev-DyJp3t2EQ
    enableAuth: false
  jwtConfig:
    enableJwt: false
  redis-config:
    host: _DO_NOT_COMMIT_
    port: 6380
    password: _DO_NOT_COMMIT_
    use-ssl: true
    command-timeout-ms: 1000
    ttlInMinutes: 60
    enableLandingPageCache: false
    inClusterMode: true
    clusterHosts: _DO_NOT_COMMIT_

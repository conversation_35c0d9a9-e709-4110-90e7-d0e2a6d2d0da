package com.illumio.data;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.IpClassificationFlowWriter;
import com.illumio.data.components.IpClassificationLookup;
import com.illumio.data.components.ResourceIdFlowReader;
import com.illumio.data.configuration.IpClassificationMmdbConfig;

import lombok.SneakyThrows;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.test.StepVerifier;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

@ExtendWith(MockitoExtension.class)
class IpClassificationMmdbPipelineTest {
    @Mock
    KafkaReceiver<String, String> kafkaReceiver;
    ResourceIdFlowReader resourceIdFlowReader;
    @Mock IpClassificationLookup ipClassificationLookup;
    @Mock IpClassificationFlowWriter ipClassificationFlowWriter;
    @Mock List<KafkaSender<String, String>> kafkaSenders;
    @Mock
    Scheduler ipClassificationScheduler;
    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    IpClassificationMmdbConfig ipClassificationMmdbConfig;
    IpClassificationMmdbPipeline ipClassificationMmdbPipeline;

    @SneakyThrows
    public static String readFromStream(InputStream inputStream) {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        for (int length; (length = inputStream.read(buffer)) != -1; ) {
            result.write(buffer, 0, length);
        }
        return result.toString(StandardCharsets.UTF_8);
    }

    @BeforeEach
    void setup() {
        ObjectMapper objectMapper = new ObjectMapper();
        resourceIdFlowReader = new ResourceIdFlowReader(objectMapper);
        when(ipClassificationMmdbConfig.getKafkaSenderConfig().getSinkTopic()).thenReturn("sink");

        ipClassificationMmdbPipeline =
                new IpClassificationMmdbPipeline(
                        kafkaReceiver,
                        kafkaSenders,
                        resourceIdFlowReader,
                        ipClassificationLookup,
                        ipClassificationFlowWriter,
                        ipClassificationMmdbConfig,
                        ipClassificationScheduler);
    }

    /**
     * Expects to drop the record in case of parsing error.
     */
    @Test
    void testBadRecord() {
        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", "value");

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                ipClassificationMmdbPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .thenAwait(Duration.ofSeconds(1))
                .expectNextCount(1)
                .verifyComplete();
    }

    /**
     * Expects the json to be returned without decoration.
     */
    @Test
    void testEmptyJson() {
        JsonNode jsonNode = mock(JsonNode.class);
        when(ipClassificationLookup.maybeAddIpClassification(any())).thenReturn(Mono.just(jsonNode));
        when(ipClassificationFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/empty-json.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(receiverRecord1 -> ipClassificationMmdbPipeline.processConsumerRecord(receiverRecord1))
                )
                .expectSubscription()
                .expectNextCount(1)
                .verifyComplete();
    }

    @Test
    void testOneRecord() {
        JsonNode jsonNode = mock(JsonNode.class);
        when(ipClassificationLookup.maybeAddIpClassification(any())).thenReturn(Mono.just(jsonNode));
        when(ipClassificationFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/1-record.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(receiverRecord1 -> ipClassificationMmdbPipeline.processConsumerRecord(receiverRecord1))
                )
                .expectSubscription()
                .expectNextCount(1)
                .verifyComplete();
    }
}

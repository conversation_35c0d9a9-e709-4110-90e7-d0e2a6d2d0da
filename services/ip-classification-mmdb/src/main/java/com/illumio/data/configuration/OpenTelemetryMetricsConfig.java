package com.illumio.data.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;

@Configuration
public class OpenTelemetryMetricsConfig {

    private LongCounter createCounter(OpenTelemetry openTelemetry, String name, String description) {
        Meter meter = openTelemetry.getMeter("ip-classification-mmdb");
        return meter.counterBuilder(name)
                .setDescription(description)
                .build();
    }

    @Bean
    public LongCounter mismatchFieldsCounter(OpenTelemetry openTelemetry) {
        return createCounter(openTelemetry, "mismatch_fields", "These fields were set upstream, but there was a mismatch between the existing value and MMDB value");
    }
}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "InventoryPersistence.fullname" . }}-env-configmap
  labels:
    {{- include "InventoryPersistence.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    spring:
      application:
        name: "inventory-persistence"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
      r2dbc:
        url: "{{.Values.spring.r2dbc.url}}"
        username: "{{.Values.spring.r2dbc.username}}"
        properties:
          sslMode: "{{.Values.spring.r2dbc.properties.sslMode}}"
    server:
      port: {{.Values.server.port}}
    retry-config:
      parameters:
        min-backoff: "{{.Values.retryConfig.parameters.minBackoff}}"
        max-retries: {{.Values.retryConfig.parameters.maxRetries}}

    inventory-persistence:
      kafka-inventory-consumer-config:
        bootstrapServers: "{{.Values.inventoryPersistence.kafkaInventoryConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.inventoryPersistence.kafkaInventoryConsumerConfig.topic}}"
        groupId: "{{.Values.inventoryPersistence.kafkaInventoryConsumerConfig.groupId}}"
        autoOffsetReset: "{{.Values.inventoryPersistence.kafkaInventoryConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.inventoryPersistence.kafkaInventoryConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.inventoryPersistence.kafkaInventoryConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.inventoryPersistence.kafkaInventoryConsumerConfig.maxPartitionFetchBytes}}"
    
      batch-processing-config:
        limit-rate-low-tide: {{.Values.inventoryPersistence.batchProcessingConfig.limitRateLowTide}}
        limit-rate-high-tide: {{.Values.inventoryPersistence.batchProcessingConfig.limitRateHighTide}}
        buffer-timeout-max-size: {{.Values.inventoryPersistence.batchProcessingConfig.bufferTimeoutMaxSize}}
        buffer-timeout-duration: "{{.Values.inventoryPersistence.batchProcessingConfig.bufferTimeoutDuration}}"
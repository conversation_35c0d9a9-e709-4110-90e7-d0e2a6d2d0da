package com.illumio.data.components;

import com.illumio.data.configuration.InventoryPersistenceConfig;
import com.illumio.data.model.InventoryObjectType;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;

import java.time.Duration;

import static com.illumio.data.util.InventoryCommonsConstants.INVENTORY_OBJECT_TYPE;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InventoryReceiverServiceTest {

    @Mock
    private KafkaReceiver<String, String> kafkaFlowReceiver;
    @Mock
    private InventoryDbService inventoryDbService;
    @Mock
    private InventoryPersistenceConfig inventoryPersistenceConfig;
    @InjectMocks
    private InventoryReceiverService inventoryReceiverService;

    private final String topicName = "inventory-v1";
    private final String recordKey = "6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af";
    private final String recordValue = "{\"createdAt\":null,\"id\":\"0197a973-fb8b-76d5-a027-660311abb216\",\"tenantId\":\"6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af\",\"type\":\"DEVICE\",\"superType\":\"DEVICE\",\"entityId\":\"*********\",\"resourceId\":\"9a9176e4-7a27-580e-a462-17ddda3f6c4b\",\"data\":\"{\\\"hostName\\\":\\\"dest-host-02\\\",\\\"ip\\\":\\\"*********\\\",\\\"macAddress\\\":\\\"00-16-17-29-45-67\\\"}\",\"hash\":\"KE4jTRLsVJjERM0H6TpiyLUpz+VETAWVAgwJKsNjgtw=\",\"lastSeenAt\":\"2025-06-25T23:37:20.011709Z\",\"updatedAt\":null}";

    @BeforeEach
    void setUp() {
        when(inventoryPersistenceConfig.getBatchProcessingConfig()).thenReturn(new InventoryPersistenceConfig.BatchProcessingConfig());
    }

    @Test
    void testConsumeInventory_successfulFlow() {
        final ConsumerRecord<String, String> record = new ConsumerRecord<>(topicName, 0, 0, recordKey, recordValue);
        record.headers().add(INVENTORY_OBJECT_TYPE, InventoryObjectType.DEVICE.name().getBytes());

        when(kafkaFlowReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(record)));
        when(inventoryDbService.persistInventoryObjects(any())).thenReturn(Mono.empty());

        inventoryReceiverService.consumeInventory();

        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() ->
                        verify(inventoryDbService).persistInventoryObjects(argThat(list ->
                                list.size() == 1 && list.get(0).getLeft() == InventoryObjectType.DEVICE &&
                                        list.get(0).getRight().equals(recordValue))));
    }

    @Test
    void testConsumeInventory_missingHeader_shouldThrow() {
        final ConsumerRecord<String, String> record = new ConsumerRecord<>(topicName, 0, 0, recordKey, recordValue);

        when(kafkaFlowReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(record)));

        inventoryReceiverService.consumeInventory();

        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() -> verifyNoInteractions(inventoryDbService));
    }

}

package com.illumio.data.components;

import com.illumio.data.annotations.retry.RetryReactiveOnError;
import com.illumio.data.configuration.InventoryPersistenceConfig;
import com.illumio.data.model.InventoryObjectType;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.springframework.stereotype.Service;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;

import java.util.Optional;

import static com.illumio.data.util.InventoryCommonsConstants.INVENTORY_OBJECT_TYPE;

@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryReceiverService {
    private final KafkaReceiver<String, String> kafkaFlowReceiver;
    private final InventoryPersistenceConfig inventoryPersistenceConfig;
    private final InventoryDbService inventoryDbService;

    @PostConstruct
    @RetryReactiveOnError
    public void consumeInventory() {
        kafkaFlowReceiver.receiveAutoAck()
                .publishOn(Schedulers.boundedElastic())
                .limitRate(inventoryPersistenceConfig.getBatchProcessingConfig().getLimitRateHighTide(),
                        inventoryPersistenceConfig.getBatchProcessingConfig().getLimitRateLowTide())
                .flatMap(records -> records)
                .bufferTimeout(inventoryPersistenceConfig.getBatchProcessingConfig().getBufferTimeoutMaxSize(),
                        inventoryPersistenceConfig.getBatchProcessingConfig().getBufferTimeoutDuration())
                .map(records ->
                        records.stream()
                                .map(this::inventoryTypeAndJsonFromKafkaRecord)
                                .toList())
                .flatMap(inventoryDbService::persistInventoryObjects)
                .onErrorContinue((throwable, o) ->
                        log.error("Error occurred while consuming inventory. Continuing inventory consumption.", throwable))
                .subscribe();
    }

    private Pair<InventoryObjectType, String> inventoryTypeAndJsonFromKafkaRecord(final ConsumerRecord<String, String> record) {
        return Optional.ofNullable(record.headers())
                .map(headers -> headers.lastHeader(INVENTORY_OBJECT_TYPE))
                .map(Header::value)
                .map(String::new)
                .map(InventoryObjectType::valueOf)
                .map(inventoryItemType -> Pair.of(inventoryItemType, record.value()))
                .orElseThrow(() -> new IllegalArgumentException(String.format("Ignoring received inventory item %s due to missing or unknown header value for %s", record.value(), INVENTORY_OBJECT_TYPE)));
    }

}

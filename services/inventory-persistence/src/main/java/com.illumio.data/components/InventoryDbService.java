package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.*;
import com.illumio.data.repositories.*;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryDbService {
    private final ObjectMapper objectMapper;
    private final DeviceRepository deviceRepository;
    private final IdentityRepository identityRepository;
    private final DeviceToDeviceRepository deviceToDeviceRepository;
    private final IdentityToDeviceRepository identityToDeviceRepository;

    private Map<InventoryObjectType, Function<List<String>, Mono<Void>>> inventoryPersistenceHandlers;

    @PostConstruct
    public void init() {
        inventoryPersistenceHandlers =
                Map.of(InventoryObjectType.DEVICE, inventoryObjectJsons ->
                                parseAndPersistInventoryObjects(inventoryObjectJsons, InventoryItem.class, deviceRepository),
                        InventoryObjectType.IDENTITY, inventoryObjectJsons ->
                                parseAndPersistInventoryObjects(inventoryObjectJsons, InventoryItem.class, identityRepository),
                        InventoryObjectType.DEVICE_DEVICE_RELATIONSHIP, inventoryObjectJsons ->
                                parseAndPersistInventoryObjects(inventoryObjectJsons, DeviceToDeviceRelationship.class, deviceToDeviceRepository),
                        InventoryObjectType.IDENTITY_DEVICE_RELATIONSHIP, inventoryObjectJsons ->
                                parseAndPersistInventoryObjects(inventoryObjectJsons, IdentityToDeviceRelationship.class, identityToDeviceRepository));
    }

    public Mono<Void> persistInventoryObjects(
            final List<Pair<InventoryObjectType, String>> inventoryTypesAndJsons) {
        final Map<InventoryObjectType, List<String>> groupedInventoryJsons = inventoryTypesAndJsons.stream()
                .collect(Collectors.groupingBy(Pair::getLeft,
                        Collectors.mapping(Pair::getRight, Collectors.toList())));
        return Mono.when(
                groupedInventoryJsons.entrySet()
                        .stream()
                        .sorted(Comparator.comparingInt(e -> switch (e.getKey()) {
                            case DEVICE -> 0;
                            case IDENTITY -> 1;
                            case DEVICE_DEVICE_RELATIONSHIP -> 2;
                            case IDENTITY_DEVICE_RELATIONSHIP -> 3;
                        })) // Persist devices and identities before relationships due to foreign key dependencies
                        .map(inventoryTypeAndJson ->
                                inventoryPersistenceHandlers.getOrDefault(inventoryTypeAndJson.getKey(),
                                                obj -> {
                                                    log.error("No inventory persistence handler for inventory {}. Skipping this object.", obj);
                                                    return Mono.empty();
                                }).apply(inventoryTypeAndJson.getValue()))
                        .toList());
    }

    private <T extends InventoryObject> Mono<Void> parseAndPersistInventoryObjects(
            final List<String> inventoryObjectJsons,
            final Class<T> inventoryObjectClass,
            final AbstractInventoryObjectRepository<T> inventoryObjectRepository) {
        final List<T> inventoryObjects = inventoryObjectJsons.stream()
                .map(inventoryObjectJson -> {
                    try {
                        return objectMapper.readValue(inventoryObjectJson, inventoryObjectClass);
                    } catch (JsonProcessingException e) {
                        log.error("Failed to parse from inventoryObjectJson={} as inventoryObjectClass={}. Skipping this object.",
                                inventoryObjectJson, inventoryObjectClass);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();
        return inventoryObjectRepository.upsertBatch(inventoryObjects);
    }

}

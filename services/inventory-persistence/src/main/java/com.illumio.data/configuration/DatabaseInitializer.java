package com.illumio.data.configuration;

import io.r2dbc.spi.ConnectionFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.r2dbc.connection.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;

import static com.illumio.data.util.InventoryPersistenceConstants.DATABASE_INITIALIZER_SCRIPT;

@Slf4j
@Service
@Lazy
@RequiredArgsConstructor
public class DatabaseInitializer implements CommandLineRunner {

    private final ConnectionFactory connectionFactory;

    @Override
    public void run(String... args) throws Exception {
        log.info("Initializing database after application startup");
        try {
            final ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
            populator.addScript(new ClassPathResource(DATABASE_INITIALIZER_SCRIPT));
            populator.setContinueOnError(false);
            populator.populate(connectionFactory)
                    .doOnSuccess(v -> log.info("Database initialized successfully"))
                    .doOnError(e -> {
                        log.error("Error initializing database. Application will now terminate", e);
                        System.exit(1);
                    })
                    .block(); // Block to ensure completion before continuing

        } catch (Exception e) {
            log.error("Error configuring database initializer. Application will now terminate", e);
            System.exit(1);
            throw e;
        }
    }

}
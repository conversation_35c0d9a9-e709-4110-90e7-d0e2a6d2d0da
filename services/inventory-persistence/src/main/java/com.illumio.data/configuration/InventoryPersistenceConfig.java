package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "inventory-persistence")
@Getter
@Setter
public class InventoryPersistenceConfig {

    private final BatchProcessingConfig batchProcessingConfig = new BatchProcessingConfig();
    private final KafkaConsumerConfig kafkaInventoryConsumerConfig = new KafkaConsumerConfig();

    @Getter
    @Setter
    public static class BatchProcessingConfig {
        private Integer limitRateLowTide = 75;
        private Integer limitRateHighTide = 100;
        private Integer bufferTimeoutMaxSize = 25;
        private Duration bufferTimeoutDuration = Duration.ofMinutes(1);
    }

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs = 30000;
        private Integer maxPollRecords = 500;
        private Integer maxPartitionFetchBytes;
    }

}

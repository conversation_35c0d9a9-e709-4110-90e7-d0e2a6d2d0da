#!/usr/bin/env groovy
//
// Copyright 2024 Illumio, Inc. All Rights Reserved.
//
// Build + Publish + Deploy pipeline:
// 1. Build and publish services
// 2. Deploy: update argocd-apps-cloudsecure-ms-sunnyvale git repo

// A set of globals to let different stages write a build message. The user is
// allowed to append to the "build_message" variable
def system_build_message = ""
def system_build_message_status = ""
def system_build_message_color = ""
def system_build_message_author = ""
def system_build_message_commit = ""
def system_build_slack_channel = ""
def build_message = ""

// It is convenient to precompute these values
def name = ""
def email = ""
def harness = ""
def is_prod = 0
def is_deploy_commit = 0
def changed_services = []
def deployable_services = []

pipeline {
  agent { label "connector" }
  parameters {
    booleanParam(name: 'OTTO_SKIP_TESTS', defaultValue: false, description: 'If set to true, we will skip tests')
    booleanParam(name: 'OTTO_PUBLISH_NONPROD', defaultValue: false, description: 'If set to true, we will publish even a non-prod build')
    booleanParam(name: 'OTTO_PUBLISH_TO_DEV', defaultValue: false, description: 'If set to true, we will publish to ecr (necessary for dev environment)')
  }

  options {
    // Keep this number of builds
    buildDiscarder(logRotator(numToKeepStr: "25"))
    // Abort after 1 hour
    timeout (time: 60, unit: "MINUTES")
    // Preserve the stashes to allow re-running a test stage
    preserveStashes(buildCount: 5)
  }

  environment {
    // What ACR to we build/publish targeting
    ACR = 'illum.azurecr.io'

    ECR = '137706418270.dkr.ecr.us-west-2.amazonaws.com'

    AWS_REGION = 'us-west-2'
    // Make the build scripts output debugging logs
    OTTO_DEBUG = true

    // Error logs from the harness will get appended to this file. Used for slack message
    OTTO_ERR_LOGFILE="${WORKSPACE}/ci/bash_lib/err.log"

    // Set this to true when testing changes to the pipeline. Setting this environment variable will do a lot:
    // * EVERY component will build
    // * "is_prod" will return true
    OTTO_TEST_PIPELINE = false
  }

  stages {
    stage("preflight") {
      steps {
        script {
          // Set environment variables from parameters. Because it's going into
          // an environment variable, the defaultValue has to be a String
          ilo_set_env_from_param('OTTO_PUBLISH_NONPROD', 'false')
          ilo_set_env_from_param('OTTO_SKIP_TESTS', 'false')
          ilo_set_env_from_param('OTTO_PUBLISH_TO_DEV', 'false')
          // Dump environment variables for nicer debugging
          harness = "${WORKSPACE}/ci/bin/connector"
          sh(label: "Jenkins env dump", script: "${harness} env")

          // Fetch git information to make a nice message
          name  = sh(label: "git: author", script: "git show --no-patch --format='%an' ${env.GIT_COMMIT}", returnStdout: true).trim()
          email = sh(label: "git: email", script: "git show --no-patch --format='%ae' ${env.GIT_COMMIT}", returnStdout: true).trim()
          system_build_message_author = "@${email.split("@")[0]} - ${email} - ${name}"
          def commit_msg = sh(label: "git: commit message", script: "git show --no-patch --format='%s' ${env.GIT_COMMIT}", returnStdout: true).trim()
          system_build_message_commit = "${commit_msg}"

          // Compute some useful global values
          def rc_is_prod = sh(label: "Determine if this is a prod build", script: "${harness} is_prod", returnStatus: true)
          is_prod = (rc_is_prod == 0)
          system_build_slack_channel = slack_channel(is_prod, harness.split("/").last())
          def rc_is_dc = sh(label: "Determine if this is a deploy commit", script: "${harness} is_deploy_commit", returnStatus: true)
          if (rc_is_dc != 0 && rc_is_dc != 40) { error("Failed to determine if this is a deploy commit or not") }
          is_deploy_commit = (rc_is_dc == 0)

          // Fetch remote & specify tracking branches.
          //
          // We rely on the existence of '/refs/remotes/origin/main' to
          // determine what the changed_services are
          withCredentials([
            usernamePassword(
              credentialsId: 'jenkins-stash-token',
              usernameVariable: 'USERNAME',
              passwordVariable: 'TOKEN'
            )
          ]) {
            def encodedUsername = URLEncoder.encode(USERNAME, "UTF-8")
            def encodedPassword = URLEncoder.encode(TOKEN, "UTF-8")
            def creds="${encodedUsername}:${encodedPassword}"
            def git_url="stash.ilabs.io/scm/data/connector.git"
            sh(
              label: "Fetch remote branches; ensure they are tracking",
              script: "git fetch 'https://${creds}@${git_url}' '+refs/heads/*:refs/remotes/origin/*'"
            )
          }
          changed_services = sh(
            label: "Get changed services",
            script: "${harness} changed_services",
            returnStdout: true
          ).split("\n").collect { it.trim() }.findAll { it }
          deployable_services = sh(
            label: "Get deployable services",
            script: "${harness} deployable_services",
            returnStdout: true
          ).split("\n").collect { it.trim() }.findAll { it }
          publishToDev = false
          if (changed_services.contains('gateway-connector')) {
            echo "gateway-connector was changed and hence will publish to ECR"
            publishToDev = true
            }
        }

        echo """Dumping preflight information:
        * git name = ${name}
        * git email = ${email}
        * git commit = ${system_build_message_commit}
        * harness = ${harness}
        * is_prod = ${is_prod}
        * is_deploy_commit = ${is_deploy_commit}
        * changed_services = [${changed_services.join(", ")}]
        * deployable_services = [${deployable_services.join(", ")}]
        * slack_channel = ${system_build_slack_channel}
        * env.OTTO_SKIP_TESTS = ${env.OTTO_SKIP_TESTS}
        * env.OTTO_PUBLISH_NONPROD = ${env.OTTO_PUBLISH_NONPROD}
        """
      }
    } // stage("preflight")

    stage("test") {
      steps {
        script {
          if (env.OTTO_SKIP_TESTS == "true") {
            echo "Skipping tests because OTTO_SKIP_TESTS is set"
            return
          }

          echo "Starting test stage"
          sh(label: "Test all services", script: "${WORKSPACE}/gradlew test")
          build_message += "• Tested all services\n"

          // Run service specific tests parallely.
          def service_test_stages = generateStageMap(changed_services, { svc ->
            sh(label: "Enforce test coverage", script: "${harness} test verify_coverage ${svc}")
          })
          parallel service_test_stages
        }
      }
    } // stage("test")

    stage("build") {
      steps {
        echo "Starting build stage"
        script {
          def stages = generateStageMap(changed_services, { svc ->
            if (publishToDev){
                sh(label: "Build service artifacts (ECR)", script: "${harness} build --svc ${svc} --acr ${ECR}")
            }
            sh(label: "Build service artifacts", script: "${harness} build --svc ${svc} --acr ${ACR}")
            def x = bm_bullet(harness, svc, "Built")
            build_message += x
          })
          parallel stages
        }
      }
    } // stage("build")

    stage("publish") {
      steps {
        script {
          // Publish gate
          echo "Pusblish to dev is set and will publish to dev {}"
          if (env.OTTO_PUBLISH_NONPROD == 'true') {
            build_message += "• Publish nonprod override is set!\n"
            echo "We bypass the 'publish gate' because we want to publish even for nonprod"
          } else if (!is_prod) {
            build_message += "• No publish (is not prod)\n"
            echo "Not publishing because this is not a prod build"
            return
          }

          // Deploy commit gate: Make sure the artifact has a unique name if it is prod
          if (!is_prod && env.OTTO_PUBLISH_NONPROD == 'true') {
            echo "We bypass the 'deploy commit gate' because we are NOT prod but we DO want to publish anyway"
          } else if (!is_deploy_commit) {
            build_message += "• No publish (is not a deploy commit)\n"
            echo "Not publishing because this is not a deploy commit"
            return
          }

          // Generate the stages
          def stages = generateStageMap(changed_services, { svc ->
          if(publishToDev){
                sh(label: "Publish service artifacts to ecr", script: """
                                  ${harness} publish --svc '${svc}' --acr '${ECR}'
                """)
            }
            sh(label: "Publish service artifacts", script: """
              ${harness} publish --svc '${svc}' --acr '${ACR}'
            """)
            def x = bm_bullet(harness, svc, "Published")
            build_message += x
          })

          // Push to Azure Container Registry:
          withCredentials([
            string(credentialsId: "connector-acr-token-username", variable: "ACR_TOKEN_USERNAME"),
            string(credentialsId: "connector-acr-token-password", variable: "ACR_TOKEN_PASSWORD"),
            // Credentials for ECR
            string(credentialsId: "AWS_ACCESS_KEY_ID_MRPINK", variable: "AWS_ECR_ACCESS_KEY_ID"),
            string(credentialsId: "AWS_SECRET_ACCESS_KEY_MRPINK", variable: "AWS_ECR_SECRET_ACCESS_KEY"),

            // Credentials for artifact
            string(credentialsId: "art-user", variable: "ART_USER"),
            string(credentialsId: "art-user-passphrase", variable: "ART_PASSWORD"),
          ]) {
            parallel stages
          }
        }
      }
    } // stage("publish")

    stage("deploy") {
      steps {
        script {
          // Deploy gate
          echo "Is publishing to dev set ${publishToDev}"
          if (!is_prod) {
            build_message += "• No deploy (is not prod)\n"
            echo "Not deploying because this is not a prod build"
            return
          }

          // Deploy gate
          if (!is_deploy_commit) {
            build_message += "• No deploy (is not a deploy commit)\n"
            echo "Not deploying because this is not a deploy commit"
            return
          }

          // Deploy gate
          if (deployable_services.size() == 0) {
            build_message += "• No deploy (no deployable services)\n"
            echo "Not deploying because there are no deployable services"
            return
          }

          if (publishToDev){
            argocdGitDev(name, email, system_build_slack_channel, {
                sh(label: "Deploying to dev stage", script: "${harness} deploy")
            })
            build_message += "• Deployed to dev\n"
          }
          // Do work (update argocd-apps git repo)
          argocdGitSunnyvale(name, email, system_build_slack_channel, {
            sh(label: "Deploying in dev", script: "${harness} deploy")
          })
          build_message += "• Deployed!\n"
        }
      }
    } // stage("deploy")

    stage("postflight") {
      steps {
        echo "Creating this stage so Blue Ocean puts all the steps from 'post' into a new stage"
      }
    }
  }

  post {
    // Configure the message
    always {
      node("connector") {
        script {
          // Add "git summary" information to the message
          system_build_message += "\nRun information\n"
          system_build_message += "• Author: ${system_build_message_author}\n"
          system_build_message += "• Job: ${slack_jenkins_links()}\n"
          system_build_message += "• Commit: ${system_build_message_commit}\n"
          system_build_message += "• Commit Hash: ${env.GIT_COMMIT}\n"

          // * If it's a PR, add a link to the bitbucket PR
          if ( env.CHANGE_ID ) {
            system_build_message += "• <https://stash.ilabs.io/projects/DATA/repos/connector/pull-requests/${env.CHANGE_ID}/overview|PR on Stash>\n"
          }
          // * If the branch is named after a jira, then link to the Jira ticket
          jira_ticket = find_jira(BRANCH_NAME)
          if ( jira_ticket ) {
            system_build_message += "• <https://jira.illum.io/browse/${jira_ticket}|Jira ticket - ${jira_ticket}>\n"
          }

          if ( build_message ) {
            system_build_message += build_message
          }

          // If this file exists, then forward its contents to slack
          if ( fileExists(OTTO_ERR_LOGFILE) ) {
            system_build_message += """
Error logs from the harness:
```
${readFile(OTTO_ERR_LOGFILE).trim()}
```
"""
          }
        }
      } // node("connector")
    }

    success {
      script {
        system_build_message_status = "SUCCESS"
        system_build_message_color = "good"
      }
    }

    unstable {
      script {
        system_build_message_status = "BUILD PASSED, PUSH FAILED"
        system_build_message_color = "warning"
      }
    }

    failure {
      script {
        system_build_message_status = "BUILD FAILED"
        system_build_message_color = "danger"
      }
    }

    // Send the message
    cleanup {
      withCredentials([
        string(credentialsId: "slack-jenkins-goldentests-token", variable: "SLACK_TOKEN")
      ]) {
        script {
          def msg = "${harness.split("/").last()} build pipeline: " + system_build_message_status + "\n" + system_build_message
          echo "Sending a slack message | system_build_slack_channel='${system_build_slack_channel}' color='${system_build_message_color}', message='${msg}'"
          slackSend(
            teamDomain: "illumio",
            token: SLACK_TOKEN,
            channel: system_build_slack_channel,
            color: system_build_message_color,
            message: msg
          )
        }
      }
    }
  } // end post
} // end pipeline

// turn a URL encoded job into a branchname
def url_decode(url) {
  old_url = "${url}"
  encode = [
    "%2F": "/"
  ]
  for (str in encode.keySet()) {
    // println("Replacing '" + str + "' with '" + encode[str] + "'")
    url = url.replace(str, encode[str])
  }
  return url
  println("Decoding URL: ${old_url} -> ${url}")
}

// Given a branchname, extract the "BLUE-XXXXX" string
def find_jira(branchname) {
  return branchname.find(/BLUE-\d+/)
}

// Collate Jenkins content into a nice slack link
def slack_jenkins_links() {
  return "<${env.JOB_URL}|${url_decode(env.JOB_NAME)}> - Build #${env.BUILD_NUMBER} - <${env.RUN_DISPLAY_URL}|blue ocean>"
}

def generateStageMap(svcs, closure) {
  def parallelStages = [:]
  svcs.each { svc ->
    parallelStages[svc] = {
      stage(svc) {
        closure(svc)
      }
    }
  }
  return parallelStages
}

// Check out the git repo for argocd-app & run the closure. The closure accepts
// the app as the argument

def argocdGitDev(name, email, system_build_slack_channel, closure) {
  echo "Calling argocdGit for Dev"
  def argocdGitopsUrl = "ssh://******************:7999/cloudops/argocd-apps-cloudsecure-dev.git"
  def argocdGitopsBranch = "main"
  def argocdGitopsDir = "tmp-argocd-dev"

  withCredentials([
    sshUserPrivateKey(
      credentialsId: "jenkins-jar11-stash",
      keyFileVariable: "SSH_KEY",
      usernameVariable: "SSH_USER")
    ]) {
    withEnv(["GIT_SSH_COMMAND=ssh -o StrictHostKeyChecking=no -o User=${SSH_USER} -i ${SSH_KEY}"]) {
      retry(3) {
        checkout([
          $class: "GitSCM",
          branches: [[name: argocdGitopsBranch]],
          userRemoteConfigs: [[credentialsId: "jenkins-jar11-stash", url: argocdGitopsUrl]],
          extensions: [
            [$class: "CleanBeforeCheckout"],
            [$class: "RelativeTargetDirectory", relativeTargetDir: argocdGitopsDir]
          ]
        ])
        dir(argocdGitopsDir) {
          sh(label: "Configure git & checkout ${argocdGitopsBranch}", script: """
            set -eux
            git config --local user.name "${system_build_slack_channel}"
            git config --local user.email "${email}"
            git status
            git checkout ${argocdGitopsBranch}
            git status
            git reset --hard origin/${argocdGitopsBranch}
            git status
          """)
          closure()
          commitMsg = generateCommitMsg(name, system_build_slack_channel)
          sh(label: "Add, commit, and push changes", script: """
            set -eux
            git status
            git add .
            git status
            git diff --staged
            git status
            git commit -m "${commitMsg}"
            git status
            git push origin ${argocdGitopsBranch}
            git status
          """)
          sh(label: "Show changes", script: "git show")
        }
      }
    }
  }
}

def argocdGitSunnyvale(name, email, system_build_slack_channel, closure) {
  echo "Calling argocdGit for Sunnyvale"
  def argocdGitopsUrl = "*********************:v3/illum-azops/illum-cloudops/argocd-apps-cloudsecure-ms-sunnyvale"
  def argocdGitopsBranch = "main"
  def argocdGitopsDir = "tmp-argocd-sunnyvale"

  withCredentials([
    sshUserPrivateKey(
      credentialsId: "jenkins-illum-azops-illum-cloudops-ssh",
      keyFileVariable: "SSH_KEY",
      usernameVariable: "SSH_USER")
    ]) {
    withEnv(["GIT_SSH_COMMAND=ssh -o StrictHostKeyChecking=no -o User=${SSH_USER} -i ${SSH_KEY}"]) {
      retry(3) {
        checkout([
          $class: "GitSCM",
          branches: [[name: argocdGitopsBranch]],
          userRemoteConfigs: [[credentialsId: "jenkins-illum-azops-illum-cloudops-ssh", url: argocdGitopsUrl]],
          extensions: [
            [$class: "CleanBeforeCheckout"],
            [$class: "RelativeTargetDirectory", relativeTargetDir: argocdGitopsDir]
          ]
        ])
        dir(argocdGitopsDir) {
          sh(label: "Configure git & checkout ${argocdGitopsBranch}", script: """
            set -eux
            git config --local user.name "${system_build_slack_channel}"
            git config --local user.email "${email}"
            git status
            git checkout ${argocdGitopsBranch}
            git status
            git reset --hard origin/${argocdGitopsBranch}
            git status
          """)
          closure()
          commitMsg = generateCommitMsg(name, system_build_slack_channel)
          sh(label: "Add, commit, and push changes", script: """
            set -eux
            git status
            git add .
            git status
            git diff --staged
            git status
            git commit -m "${commitMsg}"
            git status
            git push origin ${argocdGitopsBranch}
            git status
          """)
          sh(label: "Show changes", script: "git show")
        }
      }
    }
  }
}

def generateCommitMsg(name, system_build_slack_channel) {
  def msg = [
    "Deploy kicked off by ${name} committing to ${system_build_slack_channel}",
    "",
    "commit='${env.GIT_COMMIT}'"
  ]
  return msg.join("\n")
}

def bm_bullet(harness, svc, action) {
  def vers = sh(label: "Get version", script: "${harness} svc_vers --svc ${svc}", returnStdout: true).trim()
  def ans = "• ${action} `${svc}:${vers}`\n"
  echo "Computed build message bullet: '${ans}'"
  return ans
}

// Generate a channel name. If the pipeline_name is 'XXX' then we will generate:
//
//   jenkins-private-XXX
//   jenkins-XXX
//
// The 'private' channels are for PR builds. The non-privates are for real builds.
def slack_channel(is_prod, pipeline_name) {
  def x = [pipeline_name]
  if (!is_prod) {
    x.add(0, "private")
  }
  x.add(0, "jenkins")
  return x.join("-")
}


/**
 * Sets an environment variable from a Jenkins parameter if it exists.
 * If the parameter does not exist, it sets the environment variable to a default value.
 * Ensures that the value is a string... It is an environment variable.
 *
 * @param paramName The name of the parameter to check.
 * @param defaultValue The default value to use if the parameter is not present.
 */
def ilo_set_env_from_param(String paramName, String defaultValue) {
  if (!params.containsKey(paramName)) {
    echo "Warning: Parameter not found. Using default | paramName='${paramName}' defaultValue='${defaultValue}'"
    env."${paramName}" = defaultValue.toBoolean()
    return
  }

  def paramValue = params."${paramName}"
  if (!(paramValue.toString().toLowerCase() in ['true', 'false'])) {
    echo "Warning: Parameter is not a valid boolean string. Using default | paramName='${paramName}' defaultValue='${defaultValue}'"
    env."${paramName}" = defaultValue.toBoolean()
    return
  }

  echo "Successfully set environment variable from parameter | paramName='${paramName}' paramValue='${paramValue}'"
  env."${paramName}" = paramValue.toString().toBoolean()
}

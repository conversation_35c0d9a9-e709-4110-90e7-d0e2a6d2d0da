package com.illumio.data.model.generalFilters;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.illumio.data.model.constants.Fields;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.ImmutablePair;

/**
 * common logic for all leaf filter which is single level and do NOT contains logic relationship
 */

@Setter
@Getter
public abstract class LeafFilter extends BaseFilter {
    protected String field;

    @JsonIgnore
    public boolean isNumericType() {
        return Fields.isNumericField(this.field);
    }

    @JsonIgnore
    public boolean isTimestampType() {
        return Fields.isTimeStampField(this.field);
    }

    @JsonIgnore
    public String getDBColumn() {
        return Fields.getTableColumnNameByFieldKey(this.field);
    }

    /**
     * Base implementation to check if the leaf filter is well-formatted.
     * Checks if the field is not null and is a valid field key.
     * Subclasses should override and call super.isWellFormatted() first.
     *
     * @return a Pair containing a boolean indicating if the filter is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        if (field == null || field.isEmpty()) {
            return new ImmutablePair<>(false, "Field is null or empty");
        }
        if (Fields.getTableColumnNameByFieldKey(field) == null) {
            return new ImmutablePair<>(false, "Invalid field key: " + field);
        }
        return new ImmutablePair<>(true, null);
    }
}

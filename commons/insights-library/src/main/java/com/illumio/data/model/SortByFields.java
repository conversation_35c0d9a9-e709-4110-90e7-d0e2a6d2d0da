package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.illumio.data.model.constants.Fields;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SortByFields extends DefaultFieldTransfer implements KQLProvider, WellFormattedInput {
    private String field;
    private String order;


    @JsonIgnore
    public String getAdjustedField() {
        /**
         * Sometimes, the sort by fields need to map to a different fields in database, for
         * example, sort by "bytes" -> "sent_bytes" To avoid add customized code in multiple
         * place, add the following logic
         */
        String columnName = this.transformToKQLColumn(this.getField());
        if (columnName == null) {
            return null;
        }

        Fields adjust = Fields.getFieldByTableColumnName(columnName);
        if (adjust == null) { return null;}

        return adjust.getFieldKey();
    }

    /**
     * Bytes do NOT have corresponding column in the database
     * currently map to "sent_bytes"
     * @param field
     * @return
     */
    @Override
    public String transformToKQLColumn(String field) {
        if( field.equals(Fields.BYTES.getFieldKey()) ) {
            return super.transformToKQLColumn(Fields.SENT_BYTES.getFieldKey());
        }

        return super.transformToKQLColumn(field);
    }

    /**
     * For multiple sorting fields, the correct logic is
     * " | sort by FlowCount desc, SrcIP desc "
     *
     * So only return partial KQL for each sorting field
     * @return
     */
    @Override
    public String buildKQL() {
        String column = this.transformToKQLColumn(field);
        if (column == null || column.isEmpty()) {
            return "";
        }

        return " %s %s ".formatted(column, order);
    }

    @JsonIgnore
    public boolean isAscending() {
        return this.order.equalsIgnoreCase("asc");
    }

    @JsonIgnore
    public SortByFields getOppositeSortByFields() {
        return new SortByFields(this.getField(),
            this.isAscending()? "desc": "asc"
        );
    }

    public static String buildKQLForMultiples(List<SortByFields> list){
        String combined = list.stream()
            .map(SortByFields::buildKQL)
            .collect(Collectors.joining(","));

        return "| sort by %s".formatted(combined);
    }

    /**
     * Validates if the SortByFields is well-formatted.
     * A well-formatted SortByFields must have a non-null field that exists in the Fields enum
     * and a valid order ("asc" or "desc").
     *
     * @return a Pair containing a boolean indicating if the input is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        if (field == null || field.isEmpty()) {
            return new ImmutablePair<>(false, "Field is null or empty");
        }

        // Check if field is in Fields enum
        if (!Fields.getFieldKeySet().contains(field)) {
            return new ImmutablePair<>(false, "Invalid field: " + field);
        }

        if (order == null || order.isEmpty()) {
            return new ImmutablePair<>(false, "Order is null or empty");
        }

        if (!"asc".equalsIgnoreCase(order) && !"desc".equalsIgnoreCase(order)) {
            return new ImmutablePair<>(false, "Invalid order: " + order + ". Must be 'asc' or 'desc'");
        }

        return new ImmutablePair<>(true, null);
    }
}

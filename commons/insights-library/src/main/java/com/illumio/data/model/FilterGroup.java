package com.illumio.data.model;

import java.util.List;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FilterGroup implements InnerKQLProvider, KQLProvider {
    /**
     * Due to the recursive, the class variable will be List<InnerKQLProvider> instead of
     * List<Filters>
     */
    private List<InnerKQLProvider> filtersList;

    @Builder.Default private boolean orRelation = false;

    @Override
    public String buildKQL() {
        return "| where " + buildInnerKQL();
    }

    @Override
    public String buildInnerKQL() {
        String operator = orRelation ? " or " : " and ";
        return this.filtersList.stream()
                .map(filter -> "(" + filter.buildInnerKQL() + ")")
                .collect(Collectors.joining(operator));
    }
}

package com.illumio.data.model;

import com.illumio.data.model.constants.Fields;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultFieldTransfer implements FieldTransfer {
    @Override
    public String transformToKQLColumn(String field) {
        String result = Fields.getTableColumnNameByFieldKey(field);
        if (result == null || result.isEmpty()) {
            log.error("Field {} not found", field);
            return "";
        }

        return result;
    }
}

package com.illumio.data.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;

public class TimeRangeSplitter {

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;
    public static final long HOURS_IN_DAY = 24L;
    public static final long HOURS_IN_WEEK = 168L;

    public static long hourDifference(String startTimeStr, String endTimeStr) {
        LocalDateTime start = LocalDateTime.parse(startTimeStr, DATE_TIME_FORMATTER);
        LocalDateTime end = LocalDateTime.parse(endTimeStr, DATE_TIME_FORMATTER);
        return ChronoUnit.HOURS.between(start, end);
    }

    public static String roundToHour(String timestamp) {
        LocalDateTime time = LocalDateTime.parse(timestamp, DATE_TIME_FORMATTER);
        return time.truncatedTo(ChronoUnit.HOURS).format(DATE_TIME_FORMATTER);
    }

    public static List<Pair<String, String>> splitIntoBuckets(String startTimeStr, String endTimeStr, ChronoUnit unit) {
        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, DATE_TIME_FORMATTER);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, DATE_TIME_FORMATTER);

        return splitIntoBuckets(startTime, endTime, unit);
    }

    public static List<Pair<String, String>> splitIntoBuckets(LocalDateTime start, LocalDateTime end, ChronoUnit unit) {
        List<Pair<String, String>> buckets = new ArrayList<>();
        LocalDateTime bucketStart = start;

        while (bucketStart.isBefore(end)) {
            LocalDateTime bucketEnd = bucketStart.plus(1, unit);
            if (bucketEnd.isAfter(end)) {
                bucketEnd = end;
            }
            buckets.add(Pair.of(bucketStart.format(DATE_TIME_FORMATTER), bucketEnd.format(DATE_TIME_FORMATTER)));
            bucketStart = bucketEnd;
        }

        return buckets;
    }
}

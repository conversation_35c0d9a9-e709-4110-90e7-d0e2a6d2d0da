package com.illumio.data.util;

import java.util.concurrent.TimeUnit;

public class TimeUnitUtil {
    public static TimeUnit parseTimeUnit(String input) {
        if (input == null || input.isEmpty()){
            throw new IllegalArgumentException("Empty input");
        }

        try {
            return TimeUnit.valueOf(input.toUpperCase());
        } catch (Exception e) {
            // ignore for now
        }

        switch (input.toLowerCase()) {
            case "ns": case "nanoseconds": return TimeUnit.NANOSECONDS;
            case "us": case "microseconds": return TimeUnit.MICROSECONDS;
            case "ms": case "milliseconds": return TimeUnit.MILLISECONDS;
            case "s": case "sec": case "seconds": return TimeUnit.SECONDS;
            case "m": case "min": case "minutes": return TimeUnit.MINUTES;
            case "h": case "hours": return TimeUnit.HOURS;
            case "d": case "days": return TimeUnit.DAYS;
            default: throw new IllegalArgumentException("Unknown time unit: " + input);
        }
    }
}

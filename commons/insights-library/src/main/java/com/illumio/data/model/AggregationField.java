package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.util.TimeUnitUtil;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
/**
 * general model for aggregation related fields
 */
public class AggregationField implements WellFormattedInput {
    private String aggregatedTime;
    private List<String> aggregatedBy;
    private int stepNumber;
    private String stepUnit;

    // matchers are the fields that are used to filter the data
    private List<Filters> filters;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Filters {
        private String name;
        private String value;
    }

    @JsonIgnore
    public String getStep(){
        if (TimeUnitUtil.parseTimeUnit(stepUnit)==TimeUnit.HOURS){
            return stepNumber + "h";
        }
        return stepNumber + "d";
    }

    @JsonIgnore
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        try {
            TimeUnit timeUnit = TimeUnitUtil.parseTimeUnit(stepUnit);
            // only support hour and day
            if (timeUnit != TimeUnit.HOURS && timeUnit != TimeUnit.DAYS) {
                return Pair.of(false, "Invalid time unit: " + stepUnit);
            }
        } catch (Exception e) {
            return Pair.of(false, "Invalid time unit: " + stepUnit);
        }

        if (this.stepNumber <= 0) {
            return Pair.of(false, "Invalid step number: " + stepNumber);
        }

        if (Fields.getFieldByFieldKey(aggregatedTime) == null) {
            return Pair.of(false, "Invalid aggregated time: " + aggregatedTime);
        }

        for(String tmp: aggregatedBy) {
            if (Fields.getFieldByFieldKey(tmp) == null) {
                return Pair.of(false, "Invalid aggregated by: " + tmp);
            }
        }

        if ( aggregatedBy.stream().distinct().count() != aggregatedBy.size() ){
            return Pair.of(false, "Duplicate aggregated by: " + aggregatedBy);
        }

        if (this.filters != null) {
            for (Filters filters : this.filters) {
                if (Fields.getFieldByFieldKey(filters.getName()) == null) {
                    return Pair.of(false, "Invalid matcher name: " + filters.getName());
                }
            }
        }

        if ( filters.stream()
            .map(m -> m.name)
            .distinct()
            .count() != filters.size() ){
            return Pair.of(false, "Duplicate matcher name: " + filters);
        }

        return Pair.of(true, null);
    }
}

package com.illumio.data.service;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.Claims;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;


@Component
public class JWTService {
    @Value("${jwt.secret}")
    private String secretKeyString;

    public static final String ISSUED_AT = "iat";
    public static final String EXPIRATION_TIME = "exp";

    public Claims decodePermissionsJwt(String jwt) throws Exception {
        return Jwts.parser()
            .setSigningKey(this.secretKeyString.getBytes())
            .parseClaimsJws(jwt)
            .getBody();
    }

    public Pair<Boolean, String> validateJWT(MultiValueMap<String, String> headers, String tenantId) {
        String jwtKey = "jwt";
        String tenantKey = "tenant";

        // no jwt, return false
        if (!headers.containsKey(jwtKey)){
            return Pair.of(false, "No JWT provided");
        }

        try {
            Claims claims = this.decodePermissionsJwt(headers.getFirst(jwtKey));
            Object obj = claims.get(tenantKey);
            boolean isValid = obj != null && obj.equals(tenantId);
            if (!isValid) {
                return Pair.of(false, "Tenant ID in JWT does not match the tenant ID in the request");
            }
            return Pair.of(true, null);
        }
        catch(Exception e){
            return Pair.of(false, e.getMessage());
        }
    }
}

package com.illumio.data.mockKusto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import java.util.Map;

public class KustoQueryParserTest {

    @Test
    void testBasicQuery() {
        String query = "TestTable | project id, name | where active = true | sort by score desc";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertEquals("TestTable", parser.getTableName());
        assertEquals(2, parser.getSelectedColumns().size());
        assertTrue(parser.getSelectedColumns().contains("id"));
        assertTrue(parser.getSelectedColumns().contains("name"));
        assertEquals(1, parser.getFilterConditions().size());
        assertEquals("active", parser.getFilterConditions().get(0).getColumn());
        assertEquals("=", parser.getFilterConditions().get(0).getOperator());
        assertEquals(true, parser.getFilterConditions().get(0).getValue());
        assertEquals("score", parser.getSortColumn());
        assertFalse(parser.isSortAscending());
    }

    @Test
    void testInOperator() {
        String query = "TestTable | where Port in (80, 443, 8080)";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertEquals("TestTable", parser.getTableName());
        assertEquals(1, parser.getFilterConditions().size());

        KustoQueryParser.FilterCondition condition = parser.getFilterConditions().get(0);
        assertEquals("Port", condition.getColumn());
        assertEquals("in", condition.getOperator());
        assertTrue(condition.getValue() instanceof List);

        @SuppressWarnings("unchecked")
        List<Object> values = (List<Object>) condition.getValue();
        assertEquals(3, values.size());
        assertTrue(values.contains(80));
        assertTrue(values.contains(443));
        assertTrue(values.contains(8080));

        // Test evaluation
        assertTrue(condition.evaluate(80));
        assertTrue(condition.evaluate(443));
        assertTrue(condition.evaluate(8080));
        assertFalse(condition.evaluate(22));
    }

    @Test
    void testNotInOperator() {
        String query = "TestTable | where Protocol !in (tcp, udp)";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertEquals(1, parser.getFilterConditions().size());

        KustoQueryParser.FilterCondition condition = parser.getFilterConditions().get(0);
        assertEquals("Protocol", condition.getColumn());
        assertEquals("!in", condition.getOperator());

        @SuppressWarnings("unchecked")
        List<Object> values = (List<Object>) condition.getValue();
        assertEquals(2, values.size());
        assertTrue(values.contains("tcp"));
        assertTrue(values.contains("udp"));

        // Test evaluation
        assertFalse(condition.evaluate("tcp"));
        assertFalse(condition.evaluate("udp"));
        assertTrue(condition.evaluate("icmp"));
    }

    @Test
    void testInCaseInsensitiveOperator() {
        String query = "TestTable | where Protocol in~ (TCP, UDP)";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertEquals(1, parser.getFilterConditions().size());

        KustoQueryParser.FilterCondition condition = parser.getFilterConditions().get(0);
        assertEquals("Protocol", condition.getColumn());
        assertEquals("in~", condition.getOperator());

        @SuppressWarnings("unchecked")
        List<Object> values = (List<Object>) condition.getValue();
        assertEquals(2, values.size());

        // Test case-insensitive evaluation
        assertTrue(condition.evaluate("tcp"));
        assertTrue(condition.evaluate("TCP"));
        assertTrue(condition.evaluate("udp"));
        assertTrue(condition.evaluate("UDP"));
        assertFalse(condition.evaluate("icmp"));
    }

    @Test
    void testNotInCaseInsensitiveOperator() {
        String query = "TestTable | where Protocol !in~ (TCP, UDP)";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertEquals(1, parser.getFilterConditions().size());

        KustoQueryParser.FilterCondition condition = parser.getFilterConditions().get(0);
        assertEquals("Protocol", condition.getColumn());
        assertEquals("!in~", condition.getOperator());

        // Test case-insensitive evaluation
        assertFalse(condition.evaluate("tcp"));
        assertFalse(condition.evaluate("TCP"));
        assertFalse(condition.evaluate("udp"));
        assertFalse(condition.evaluate("UDP"));
        assertTrue(condition.evaluate("icmp"));
    }

    @Test
    void testMultipleConditions() {
        String query = "TestTable | where Port in (80, 443) and Protocol in~ (TCP, UDP)";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertEquals(2, parser.getFilterConditions().size());

        // First condition
        KustoQueryParser.FilterCondition portCondition = parser.getFilterConditions().get(0);
        assertEquals("Port", portCondition.getColumn());
        assertEquals("in", portCondition.getOperator());

        @SuppressWarnings("unchecked")
        List<Object> portValues = (List<Object>) portCondition.getValue();
        assertEquals(2, portValues.size());
        assertTrue(portValues.contains(80));
        assertTrue(portValues.contains(443));

        // Second condition
        KustoQueryParser.FilterCondition protocolCondition = parser.getFilterConditions().get(1);
        assertEquals("Protocol", protocolCondition.getColumn());
        assertEquals("in~", protocolCondition.getOperator());

        @SuppressWarnings("unchecked")
        List<Object> protocolValues = (List<Object>) protocolCondition.getValue();
        assertEquals(2, protocolValues.size());
    }

    @Test
    void testSingleValueInOperator() {
        String query = "TestTable | where Port in (80)";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertEquals(1, parser.getFilterConditions().size());

        KustoQueryParser.FilterCondition condition = parser.getFilterConditions().get(0);
        assertEquals("Port", condition.getColumn());
        assertEquals("in", condition.getOperator());

        @SuppressWarnings("unchecked")
        List<Object> values = (List<Object>) condition.getValue();
        assertEquals(1, values.size());
        assertTrue(values.contains(80));

        // Test evaluation
        assertTrue(condition.evaluate(80));
        assertFalse(condition.evaluate(443));
    }

    @Test
    void testSortColumnParsing() {
        // Test with the specific query from the issue
        String query = "DecoratedFlows | where SrcIP in (\"***********\") | project SrcIP, DestIP, SentBytes | sort by  SentBytes asc";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertTrue(parser.hasSortColumn());
        assertEquals("SentBytes", parser.getSortColumn());
        assertTrue(parser.isSortAscending());

        // Test with extra spaces around the sort column
        String query2 = "DecoratedFlows | sort by  SentBytes  desc";
        KustoQueryParser parser2 = new KustoQueryParser(query2);

        assertTrue(parser2.hasSortColumn());
        assertEquals("SentBytes", parser2.getSortColumn());
        assertFalse(parser2.isSortAscending());
    }

    @Test
    void testNestedOrCondition() {
        // Test with the specific query from the issue
        String query = "DecoratedFlows | where (SrcIP in (\"***********\") and SrcId in (\"D27CE85C472B\")) or (SrcIP in (\"************\"))";
        KustoQueryParser parser = new KustoQueryParser(query);

        assertTrue(parser.hasFilter());
        System.out.println("Filter conditions size: " + parser.getFilterConditions().size());
        for (int i = 0; i < parser.getFilterConditions().size(); i++) {
            System.out.println("Condition " + i + ": " + parser.getFilterConditions().get(i).getClass().getSimpleName());
        }
        assertEquals(1, parser.getFilterConditions().size());

        // The top-level condition should be a composite with OR operator
        KustoQueryParser.FilterCondition condition = parser.getFilterConditions().get(0);
        assertTrue(condition instanceof KustoQueryParser.CompositeFilterCondition);

        KustoQueryParser.CompositeFilterCondition composite = (KustoQueryParser.CompositeFilterCondition) condition;
        assertEquals("or", composite.operator);
        assertEquals(2, composite.conditions.size());

        // First condition should be a composite AND with two conditions
        KustoQueryParser.FilterCondition leftCondition = composite.conditions.get(0);
        assertTrue(leftCondition instanceof KustoQueryParser.CompositeFilterCondition);
        KustoQueryParser.CompositeFilterCondition leftComposite = (KustoQueryParser.CompositeFilterCondition) leftCondition;
        assertEquals("and", leftComposite.operator);
        assertEquals(2, leftComposite.conditions.size());

        // Second condition should be a simple condition
        KustoQueryParser.FilterCondition rightCondition = composite.conditions.get(1);
        assertTrue(rightCondition instanceof KustoQueryParser.SimpleFilterCondition);
        KustoQueryParser.SimpleFilterCondition rightSimple = (KustoQueryParser.SimpleFilterCondition) rightCondition;
        assertEquals("SrcIP", rightSimple.getColumn());
        assertEquals("in", rightSimple.getOperator());
    }
}

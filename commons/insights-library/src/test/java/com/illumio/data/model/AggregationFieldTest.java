package com.illumio.data.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class AggregationFieldTest {

    @Test
    void testJson() throws Exception{
        AggregationField af = AggregationField.builder()
            .aggregatedTime("ingestion_time()")
            .aggregatedBy(List.of("sent_bytes", "flows"))
            .stepNumber(1)
            .stepUnit("hours")
            .filters(List.of(
                AggregationField.Filters.builder().name("source_ip").value("*******").build(),
                AggregationField.Filters.builder().name("destination_ip").value("*******").build()
            ))
            .build();

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(af);
        /**
         * {
         *    "aggregatedTime":"ingestion_time()",
         *    "aggregatedBy":[
         *       "sent_bytes",
         *       "flows"
         *    ],
         *    "stepNumber":1,
         *    "stepUnit":"HOURS",
         *    "matchers":[
         *       {
         *          "name":"source_ip",
         *          "value":"*******"
         *       },
         *       {
         *          "name":"destination_ip",
         *          "value":"*******"
         *       }
         *    ]
         * }
         */
        System.out.println(json);

        AggregationField deserialized = mapper.readValue(json, AggregationField.class);
        Assertions.assertEquals(af, deserialized);
    }
}

package com.illumio.data.mockKusto;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.LinkedHashMap;
import java.util.Map;

public class MockKustoFlowTable {
    public static final String FLOW_TABLE = "DecoratedFlows";
    public static MockKustoDatabase createKustoDatabase(){
        MockKustoDatabase db = new MockKustoDatabase();
        Map<String, Class<?>> schema = createFlowTableSchema();
        db.createTable(FLOW_TABLE, schema);
        try {
            /**
             * CSV file generated in Sunnyvale DB with
             *
             *   DecoratedFlows
             * | where IllumioTenantId == 'c881063c-f156-4293-b5ad-516f283322e8'
             *  | where ingestion_time() >= datetime('2025-05-19T18:00:00')| where ingestion_time() <= datetime('2025-05-20T18:00:00')
             * | where  (((Port  in  ( 135 )) and (Proto  in  ( 'tcp' ))))
             * | sort by  SentBytes desc
             * | take 200
             */
            db.ingestFromCsv(FLOW_TABLE, "/DecoratedFlows_Data_WithIngestTime.csv");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return db;
    }

    private static Map<String, Class<?>> createFlowTableSchema() {
        Map<String, Class<?>> schema = new LinkedHashMap<>();
        // Add all columns from the export.csv schema
        schema.put("SrcIP", String.class);
        schema.put("SrcId", String.class);
        schema.put("CSSrcId", String.class);
        schema.put("DestIP", String.class);
        schema.put("DestId", String.class);
        schema.put("CSDestId", String.class);
        schema.put("Port", Integer.class);
        schema.put("Proto", String.class);
        schema.put("SentBytes", Long.class);
        schema.put("ReceivedBytes", Long.class);
        schema.put("IllumioTenantId", String.class);
        schema.put("SrcTenantId", String.class);
        schema.put("SrcSubId", String.class);
        schema.put("SrcRegion", String.class);
        schema.put("SrcResId", String.class);
        schema.put("SrcVnetId", String.class);
        schema.put("SrcUserName", String.class);
        schema.put("DestTenantId", String.class);
        schema.put("DestSubId", String.class);
        schema.put("DestRegion", String.class);
        schema.put("DestResId", String.class);
        schema.put("DestVnetId", String.class);
        schema.put("DestUserName", String.class);
        schema.put("SrcFlowType", String.class);
        schema.put("DestFlowType", String.class);
        schema.put("SrcDeviceId", String.class);
        schema.put("SrcFirewallId", String.class);
        schema.put("SrcUserId", String.class);
        schema.put("DestDeviceId", String.class);
        schema.put("DestFirewallId", String.class);
        schema.put("DestUserId", String.class);
        schema.put("SrcResourceType", String.class);
        schema.put("DestResourceType", String.class);
        schema.put("SrcThreatLevel", Integer.class);
        schema.put("DestThreatLevel", Integer.class);
        schema.put("SrcIsWellknown", Boolean.class);
        schema.put("DestIsWellknown", Boolean.class);
        schema.put("SrcDomain", String.class);
        schema.put("DestDomain", String.class);
        schema.put("SrcCountry", String.class);
        schema.put("DestCountry", String.class);
        schema.put("SrcCity", String.class);
        schema.put("DestCity", String.class);
        schema.put("SrcCloudProvider", String.class);
        schema.put("DestCloudProvider", String.class);
        schema.put("SourceHostName", String.class);
        schema.put("SourceMACAddress", String.class);
        schema.put("SourceNTDomain", String.class);
        schema.put("SourceProcessId", String.class);
        schema.put("SourceProcessName", Integer.class);
        schema.put("SourceUserName", String.class);
        schema.put("SourceUserPrivileges", String.class);
        schema.put("DeviceAction", String.class);
        schema.put("DeviceAddress", String.class);
        schema.put("DestinationDnsDomain", String.class);
        schema.put("DestinationHostName", String.class);
        schema.put("DestinationMACAddress", String.class);
        schema.put("DestinationNTDomain", String.class);
        schema.put("DestinationProcessId", Integer.class);
        schema.put("DestinationProcessName", String.class);
        schema.put("DestinationServiceName", String.class);
        schema.put("DestinationTranslatedAddress", String.class);
        schema.put("DestinationUserName", String.class);
        schema.put("LogSeverity", String.class);
        schema.put("MaliciousIP", String.class);
        schema.put("MaliciousIPCountry", String.class);
        schema.put("MaliciousIPLatitude", Double.class);
        schema.put("MaliciousIPLongitude", Double.class);
        schema.put("LAWTenantId", String.class);
        schema.put("ThreatConfidence", String.class);
        schema.put("ThreatDescription", String.class);
        schema.put("ThreatSeverity", Integer.class);
        schema.put("StartTime", String.class);
        schema.put("EndTime", String.class);
        schema.put("SourceDnsDomain", String.class);
        schema.put("SourceServiceName", String.class);
        schema.put("SourceSystem", String.class);
        schema.put("DeviceMacAddress", String.class);
        schema.put("DeviceName", String.class);
        schema.put("DeviceOutboundInterface", String.class);
        schema.put("DeviceProduct", String.class);
        schema.put("DeviceTranslatedAddress", String.class);
        schema.put("DeviceVersion", String.class);
        schema.put("DeviceTimeZone", String.class);
        schema.put("DeviceExternalID", String.class);
        schema.put("DeviceCustomNumber3", Integer.class);
        schema.put("ReceiptTime", String.class);
        schema.put("Activity", String.class);
        schema.put("AdditionalExtensions", String.class);
        schema.put("SourceZone", String.class);
        schema.put("DestinationZone", String.class);
        schema.put("RequestURL", String.class);
        schema.put("Computer", String.class);
        schema.put("SourceLabel", String.class);
        schema.put("DestinationLabel", String.class);
        schema.put("SrcCloudTags", String.class);
        schema.put("DestCloudTags", String.class);
        schema.put("FlowCount", Integer.class);
        schema.put("PacketsReceived", Integer.class);
        schema.put("PacketsSent", Integer.class);
        schema.put("TrafficStatus", String.class);
        schema.put("SrcAccountName", String.class);
        schema.put("DestAccountName", String.class);
        schema.put("SrcResourceCategory", String.class);
        schema.put("DestResourceCategory", String.class);
        schema.put("SrcResourceName", String.class);
        schema.put("DestResourceName", String.class);
        schema.put("SrcResourceGroup", String.class);
        schema.put("DestResourceGroup", String.class);
        schema.put("SrcSubnetId", String.class);
        schema.put("DestSubnetId", String.class);
        schema.put("SrcCSLabel", String.class);
        schema.put("DestCSLabel", String.class);
        schema.put("DeviceExternalId", String.class);
        schema.put("SrcGeoRegion", String.class);
        schema.put("DestGeoRegion", String.class);
        schema.put("DestinationExternalLabel", String.class);
        schema.put("DestinationExternalLabelCategory", String.class);
        schema.put("SrcCSLabels", String.class);
        schema.put("DestCSLabels", String.class);
        schema.put("SourceExternalLabel", String.class);
        schema.put("SourceExternalLabelCategory", String.class);
        schema.put("Hops", String.class);
        schema.put("DeniedAt", String.class);
        schema.put("IngestionTime", LocalDateTime.class);

        return schema;
    }
}

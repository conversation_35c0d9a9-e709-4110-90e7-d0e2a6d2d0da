package com.illumio.data.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;

public class TimeUnitUtilTest {
    @Test
    void testValidTimeUnit() throws Exception {
        TimeUnit unit = TimeUnitUtil.parseTimeUnit("h");
        assertEquals(TimeUnit.HOURS, unit);

        unit = TimeUnitUtil.parseTimeUnit("Hours");
        assertEquals(TimeUnit.HOURS, unit);

        unit = TimeUnitUtil.parseTimeUnit("d");
        assertEquals(TimeUnit.DAYS, unit);

        unit = TimeUnitUtil.parseTimeUnit("days");
        assertEquals(TimeUnit.DAYS, unit);
    }
}

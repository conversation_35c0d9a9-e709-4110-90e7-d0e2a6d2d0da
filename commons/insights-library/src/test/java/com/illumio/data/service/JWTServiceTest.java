package com.illumio.data.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.Jwts;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Date;
import javax.crypto.SecretKey;

@SpringBootTest(classes = {JWTService.class})
@TestPropertySource(properties = {"jwt.secret=a-string-secret-at-least-256-bits-long"})
public class JWTServiceTest {
    @Autowired private JWTService jwtService;

    @Value("${jwt.secret}")
    private String secretKey;

    @Test
    void testDecode() throws Exception {
        // Literal secret string
        SecretKey key = Keys.hmacShaKeyFor(secretKey.getBytes());

        long curTime = System.currentTimeMillis();
        // Create token
        String jwt =
                Jwts.builder()
                        .setSubject("user123")
                        .claim("tenant", "af84345d-0f7a-43ad-b19f-dcce53af2b9d")
                        .setIssuedAt(new Date(curTime))
                        .setExpiration(new Date(System.currentTimeMillis() + 3600_000)) // 1 hour
                        .signWith(key)
                        .compact();

        System.out.println("JWT Token: " + jwt);

        Claims claims = jwtService.decodePermissionsJwt(jwt);
        assertEquals("user123", claims.getSubject());
        assertEquals("af84345d-0f7a-43ad-b19f-dcce53af2b9d", claims.get("tenant"));
        assertTrue(claims.getIssuedAt().getTime() >= curTime / 1000);
        assertEquals(claims.getExpiration().getTime(),
            claims.getIssuedAt().getTime() + 3600_000);
    }

    @Test
    void testInvalidJWT() throws Exception {
        Exception exception = assertThrows(
            Exception.class,
            () -> jwtService.decodePermissionsJwt("WrongJWT")
        );

        assertTrue(exception instanceof MalformedJwtException);
    }

    @Test
    void testJWTExpired() throws Exception {
        // Literal secret string
        SecretKey key = Keys.hmacShaKeyFor(secretKey.getBytes());

        long curTime = System.currentTimeMillis();
        // Create token
        String jwt =
            Jwts.builder()
                .setSubject("user123")
                .claim("role", "admin")
                .setIssuedAt(new Date(curTime - 7200_000))
                .setExpiration(new Date(System.currentTimeMillis() - 3600_000)) // 1 hour
                .signWith(key)
                .compact();

        Exception exception = assertThrows(
            Exception.class,
            () -> jwtService.decodePermissionsJwt(jwt)
        );

        assertTrue(exception instanceof ExpiredJwtException);

    }
}

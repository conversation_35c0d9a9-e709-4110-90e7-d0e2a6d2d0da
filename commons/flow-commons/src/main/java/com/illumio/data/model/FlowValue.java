package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.time.Instant;

@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Jacksonized
@AllArgsConstructor
@Builder(toBuilder = true)
public class FlowValue {
    @JsonProperty("SrcIP")
    private String SrcIP;
    private String SrcId;
    @JsonProperty("CSSrcId")
    private String CSSrcId;
    @JsonProperty("DestIP")
    private String DestIP;
    private String DestId;
    @JsonProperty("CSDestId")
    private String CSDestId;
    private Integer Port;
    private String Proto;
    private Long SentBytes;
    private Long ReceivedBytes;
    private String IllumioTenantId;
    private String SrcTenantId;
    private String SrcSubId;
    private String SrcRegion;
    private String SrcResId;
    private String SrcVnetId;
    private String SrcUserName;
    private String DestTenantId;
    private String DestSubId;
    private String DestRegion;
    private String DestResId;
    private String DestVnetId;
    private String DestUserName;
    private String SrcFlowType;
    private String DestFlowType;
    private String SrcDeviceId;
    private String SrcFirewallId;
    private String SrcUserId;
    private String DestDeviceId;
    private String DestFirewallId;
    private String DestUserId;
    private String SrcResourceType;
    private String DestResourceType;
    private String SrcThreatLevel;
    private String DestThreatLevel;
    private Boolean SrcIsWellknown;
    private Boolean DestIsWellknown;
    private String SrcDomain;
    private String DestDomain;
    private String SrcCountry;
    private String DestCountry;
    private String SrcCity;
    private String DestCity;
    private String SrcCloudProvider;
    private String DestCloudProvider;
    private String SourceHostName;
    @JsonProperty("SourceMACAddress")
    private String SourceMACAddress;
    private String SourceNTDomain;
    private String SourceProcessId;
    private String SourceProcessName;
    private String SourceUserPrivileges;
    private String DeviceAction;
    private String DeviceAddress;
    private String DestinationDnsDomain;
    private String DestinationHostName;
    @JsonProperty("DestinationMACAddress")
    private String DestinationMACAddress;
    private String DestinationNTDomain;
    private String DestinationProcessId;
    private String DestinationProcessName;
    private String DestinationServiceName;
    private String DestinationTranslatedAddress;
    private String DestinationUserPrivileges;
    private String LogSeverity;
    @JsonProperty("MaliciousIP")
    private String MaliciousIP;
    @JsonProperty("MaliciousIPCountry")
    private String MaliciousIPCountry;
    @JsonProperty("MaliciousIPLatitude")
    private String MaliciousIPLatitude;
    @JsonProperty("MaliciousIPLongitude")
    private String MaliciousIPLongitude;
    private String LawTenantId;
    private String ThreatConfidence;
    private String ThreatDescription;
    private String ThreatSeverity;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSSSSSS]'Z'", timezone = "UTC")
    private Instant StartTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSSSSSS]'Z'", timezone = "UTC")
    private Instant EndTime;
    private String SourceDnsDomain;
    private String SourceServiceName;
    private String SourceSystem;
    @JsonProperty("DeviceMACAddress")
    private String DeviceMACAddress;
    private String DeviceName;
    private String DeviceOutboundInterface;
    private String DeviceProduct;
    private String DeviceTranslatedAddress;
    private String DeviceVersion;
    private String DeviceTimeZone;
    private String DeviceExternalId;
    private String DeviceCustomerNumber3;
    private String DeviceVendor;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSSSSSS]'Z'", timezone = "UTC")
    private Instant ReceiptTime;
    private String Activity;
    private String AdditionalExtensions;
    private String SourceZone;
    private String DestinationZone;
    private String RequestUrl;
    private String SrcCloudTags;
    private String DestCloudTags;
    private Long FlowCount;
    private Long PacketsSent;
    private Long PacketsReceived;
    private String TrafficStatus;

    public FlowValue() {
        this.StartTime = null;
        this.EndTime = null;
        this.FlowCount = 0L;
        this.SentBytes = 0L;
        this.ReceivedBytes = 0L;
        this.PacketsSent = 0L;
        this.PacketsReceived = 0L;
    }
}